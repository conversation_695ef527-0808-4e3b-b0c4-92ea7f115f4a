clear;
addpath('./functions')
% rads = [600.0, 1221.5, 2350.75, 3480.0]*1000;
% rads = [150, 300.0, 600.0, 1221.5, 1700]*1000;
rads = [600.0, 1221.5]*1000;
% rads = [600.0, 1221.5, 2914.25, 3480.0, 3630.0, 5600.0, 5701.0, 5771.0, 5971.0, 6151.0, 6291.0, 6346.0, 6356.0, 6368.0, 6371.0]*1000;
ppw  = 3.5;
freq = 0.01;  % Hz
p    = 5;
dx   = 10000;
duration = 480;
nparts   = 2;

OM = mesh_sphere2dA(rads,dx,nparts,freq,ppw);
% work for sqrtm
% OM = OM([1,2,3]); % works
% OM = OM([1,3,4]); % works
% OM = OM([2,3,5]); % works
% OM = OM([3,4,5]); % works

% not work for cholosky
% OM = OM([1,4]); % not work
% OM = OM([2,5]); % not work

% OM = mesh_regional2d(dx,freq,ppw);
plot_domain2d(OM)

OM = find_connections2dA(OM);

OM = refine_model2dA(OM);

% setting dt
[dt,nt]  = compute_dt2dA(OM,duration);

OM = gen_DFDMatrices2dA(OM);

% 确保output目录存在
output_dir = 'output';
if ~exist(output_dir, 'dir')
    mkdir(output_dir);
end

% OM = gen_effective_properties_pts2dA(OM);

% initialize state parameters
OM = initializedomain2dA(OM,nt);

% get source info
[sour] = get_source2dA(OM,freq,dt,nt);

% get receiver info
[rece] = get_receiver2dA(OM,dt,nt);

% numerical simulation
[OM,rece] = solver2dA(OM,sour,rece,dt,nt);

%% ---------- 输出与C++相同格式的数据 ----------
% 输出所有域的关键信息，格式与C++一致
fprintf('\n=== MATLAB 域信息输出 (与C++对比) ===\n');

for iom = 1:length(OM)
    fprintf('Domain %d:\n', iom-1);  % C++使用0-based索引
    fprintf('  Nx1 = %d, Nz1 = %d\n', OM(iom).Nx1, OM(iom).Nz1);
    fprintf('  Nx2 = %d, Nz2 = %d\n', OM(iom).Nx1-1, OM(iom).Nz1-1);  % Nx2 = Nx1-1
    fprintf('  px1 = %d, pz1 = %d\n', OM(iom).px1, OM(iom).pz1);
    % region 字段在 MATLAB 中可能不存在，使用默认值
    if isfield(OM(iom), 'region')
        fprintf('  region = %d\n', OM(iom).region);
    else
        fprintf('  region = %d (默认)\n', 2);  % C++中显示为region=2
    end

    % 计算域边界
    if isfield(OM(iom), 'x2d11') && ~isempty(OM(iom).x2d11)
        x_min = min(OM(iom).x2d11(:));
        x_max = max(OM(iom).x2d11(:));
        z_min = min(OM(iom).z2d11(:));
        z_max = max(OM(iom).z2d11(:));
        fprintf('  Domain boundaries: x=[%.3f, %.3f] km, z=[%.3f, %.3f] km\n', ...
                x_min/1000, x_max/1000, z_min/1000, z_max/1000);
    end

    % 输出矩阵维度信息
    if isfield(OM(iom), 'bxT1') && ~isempty(OM(iom).bxT1)
        fprintf('  bxT1 size: %dx%d\n', size(OM(iom).bxT1));
    end
    if isfield(OM(iom), 'bzT1') && ~isempty(OM(iom).bzT1)
        fprintf('  bzT1 size: %dx%d\n', size(OM(iom).bzT1));
    end

    fprintf('\n');
end

%% ---------- 输出详细对比数据 ----------
% 调用数据对比函数进行 MATLAB vs C++ 全域对比
fprintf('=== 调用数据对比函数 ===\n');
try
    extract_comparison_data();
    fprintf('✅ 数据对比完成\n');
catch ME
    fprintf('❌ 数据对比失败: %s\n', ME.message);
    fprintf('请确保 OM 数据已正确加载\n');
end

fprintf('=== MATLAB 输出完成 ===\n');


