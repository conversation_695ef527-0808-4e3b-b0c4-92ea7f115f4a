{"mcpServers": {"github": {"command": "npx", "args": ["@modelcontextprotocol/server-github"], "description": "GitHub MCP - View open source C++ projects and code examples", "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "your_github_token_here"}}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"], "env": {"DEFAULT_MINIMUM_TOKENS": "10000"}}, "TaskManager": {"command": "npx", "args": ["-y", "@kazuph/mcp-taskmanager"], "env": {"TASK_MANAGER_FILE_PATH": "~/Documents/tasks.json"}}, "filesystem": {"command": "npx", "args": ["@modelcontextprotocol/server-filesystem", "D:\\project"], "description": "Filesystem MCP - Access project file system"}, "brave-search": {"command": "npx", "args": ["@modelcontextprotocol/server-brave-search"], "description": "Brave Search MCP - Search C++ technical documentation and solutions", "env": {"BRAVE_API_KEY": "your_brave_api_key_here"}}, "memory": {"command": "npx", "args": ["@modelcontextprotocol/server-memory"], "description": "Memory MCP - Remember project context and code conversion progress"}}}