=== MATLAB vs C++ 数据对比报告 ===
生成时间: 02-Aug-2025 16:59:16

=== 总体统计 ===
对比域数量: 12

=== domain_0 ===
参数对比:
  Nx1: MATLAB=37, C++=26 ❌
  Nz1: MATLAB=28, C++=34 ❌
  x_min: MATLAB=-8.637309e+05, C++=-8.637309e+05, 相对差异=0.00%
  x_max: MATLAB=-4.849601e-11, C++=-1.102182e-10, 相对差异=127.27%
  z_min: MATLAB=-1.221500e+06, C++=-1.221500e+06, 相对差异=0.00%
  z_max: MATLAB=-4.242641e+05, C++=-4.242641e+05, 相对差异=0.00%

矩阵对比:
  dxpdx11: 维度不匹配 - MATLAB: [37 28], C++: [26 34]
  dxpdx22: 维度不匹配 - MATLAB: [36 27], C++: [25 33]
  dxpdz11: 维度不匹配 - MATLAB: [37 28], C++: [26 34]
  dxpdz22: 维度不匹配 - MATLAB: [36 27], C++: [25 33]
  dzpdx11: 维度不匹配 - MATLAB: [37 28], C++: [26 34]
  dzpdx22: 维度不匹配 - MATLAB: [36 27], C++: [25 33]
  dzpdz11: 维度不匹配 - MATLAB: [37 28], C++: [26 34]
  dzpdz22: 维度不匹配 - MATLAB: [36 27], C++: [25 33]
  bxT1: 维度不匹配 - MATLAB: [37 37], C++: [26 26]
  bxT2: 维度不匹配 - MATLAB: [37 36], C++: [26 25]
  bzT1: 维度不匹配 - MATLAB: [28 28], C++: [34 34]
  bzT2: 维度不匹配 - MATLAB: [28 27], C++: [34 33]
  Jac11: 维度不匹配 - MATLAB: [37 28], C++: [26 34]
  Jac22: 维度不匹配 - MATLAB: [36 27], C++: [25 33]
  mu11: 🟢 完全相同 - 最大相对差异: 0.00e+00%, 范围: MATLAB[1.51e+10,1.51e+10], C++[1.51e+10,1.51e+10]
  mu22: 🟢 完全相同 - 最大相对差异: 0.00e+00%, 范围: MATLAB[1.51e+10,1.51e+10], C++[1.51e+10,1.51e+10]
  state_Sxx11: 维度不匹配 - MATLAB: [37 28], C++: [26 34]
  state_Sxx22: 维度不匹配 - MATLAB: [36 27], C++: [25 33]
  state_Szz11: 维度不匹配 - MATLAB: [37 28], C++: [26 34]
  state_Szz22: 维度不匹配 - MATLAB: [36 27], C++: [25 33]
  state_U12: 维度不匹配 - MATLAB: [37 27], C++: [26 33]
  state_U21: 维度不匹配 - MATLAB: [36 28], C++: [25 34]

=== domain_1 ===
参数对比:
  Nx1: MATLAB=37, C++=26 ❌
  Nz1: MATLAB=28, C++=34 ❌
  x_min: MATLAB=-2.243859e-10, C++=-2.243859e-10, 相对差异=0.00%
  x_max: MATLAB=8.637309e+05, C++=8.637309e+05, 相对差异=0.00%
  z_min: MATLAB=-1.221500e+06, C++=-1.221500e+06, 相对差异=0.00%
  z_max: MATLAB=-4.242641e+05, C++=-4.242641e+05, 相对差异=0.00%

矩阵对比:
  dxpdx11: 维度不匹配 - MATLAB: [37 28], C++: [26 34]
  dxpdx22: 维度不匹配 - MATLAB: [36 27], C++: [25 33]
  dxpdz11: 维度不匹配 - MATLAB: [37 28], C++: [26 34]
  dxpdz22: 维度不匹配 - MATLAB: [36 27], C++: [25 33]
  dzpdx11: 维度不匹配 - MATLAB: [37 28], C++: [26 34]
  dzpdx22: 维度不匹配 - MATLAB: [36 27], C++: [25 33]
  dzpdz11: 维度不匹配 - MATLAB: [37 28], C++: [26 34]
  dzpdz22: 维度不匹配 - MATLAB: [36 27], C++: [25 33]
  bxT1: 维度不匹配 - MATLAB: [37 37], C++: [26 26]
  bxT2: 维度不匹配 - MATLAB: [37 36], C++: [26 25]
  bzT1: 维度不匹配 - MATLAB: [28 28], C++: [34 34]
  bzT2: 维度不匹配 - MATLAB: [28 27], C++: [34 33]
  Jac11: 维度不匹配 - MATLAB: [37 28], C++: [26 34]
  Jac22: 维度不匹配 - MATLAB: [36 27], C++: [25 33]
  mu11: 🟢 完全相同 - 最大相对差异: 0.00e+00%, 范围: MATLAB[1.51e+10,1.51e+10], C++[1.51e+10,1.51e+10]
  mu22: 🟢 完全相同 - 最大相对差异: 0.00e+00%, 范围: MATLAB[1.51e+10,1.51e+10], C++[1.51e+10,1.51e+10]
  state_Sxx11: 维度不匹配 - MATLAB: [37 28], C++: [26 34]
  state_Sxx22: 维度不匹配 - MATLAB: [36 27], C++: [25 33]
  state_Szz11: 维度不匹配 - MATLAB: [37 28], C++: [26 34]
  state_Szz22: 维度不匹配 - MATLAB: [36 27], C++: [25 33]
  state_U12: 维度不匹配 - MATLAB: [37 27], C++: [26 33]
  state_U21: 维度不匹配 - MATLAB: [36 28], C++: [25 34]

=== domain_2 ===
参数对比:
  Nx1: MATLAB=28, C++=34 ❌
  Nz1: MATLAB=37, C++=26 ❌
  x_min: MATLAB=-1.221500e+06, C++=-1.221500e+06, 相对差异=0.00%
  x_max: MATLAB=-4.242641e+05, C++=-4.242641e+05, 相对差异=0.00%
  z_min: MATLAB=-8.637309e+05, C++=-8.637309e+05, 相对差异=0.00%
  z_max: MATLAB=1.495906e-10, C++=1.495906e-10, 相对差异=0.00%

矩阵对比:
  dxpdx11: 维度不匹配 - MATLAB: [28 37], C++: [34 26]
  dxpdx22: 维度不匹配 - MATLAB: [27 36], C++: [33 25]
  dxpdz11: 维度不匹配 - MATLAB: [28 37], C++: [34 26]
  dxpdz22: 维度不匹配 - MATLAB: [27 36], C++: [33 25]
  dzpdx11: 维度不匹配 - MATLAB: [28 37], C++: [34 26]
  dzpdx22: 维度不匹配 - MATLAB: [27 36], C++: [33 25]
  dzpdz11: 维度不匹配 - MATLAB: [28 37], C++: [34 26]
  dzpdz22: 维度不匹配 - MATLAB: [27 36], C++: [33 25]
  bxT1: 维度不匹配 - MATLAB: [28 28], C++: [34 34]
  bxT2: 维度不匹配 - MATLAB: [28 27], C++: [34 33]
  bzT1: 维度不匹配 - MATLAB: [37 37], C++: [26 26]
  bzT2: 维度不匹配 - MATLAB: [37 36], C++: [26 25]
  Jac11: 维度不匹配 - MATLAB: [28 37], C++: [34 26]
  Jac22: 维度不匹配 - MATLAB: [27 36], C++: [33 25]
  mu11: 🟢 完全相同 - 最大相对差异: 0.00e+00%, 范围: MATLAB[1.51e+10,1.51e+10], C++[1.51e+10,1.51e+10]
  mu22: 🟢 完全相同 - 最大相对差异: 0.00e+00%, 范围: MATLAB[1.51e+10,1.51e+10], C++[1.51e+10,1.51e+10]
  state_Sxx11: 维度不匹配 - MATLAB: [28 37], C++: [34 26]
  state_Sxx22: 维度不匹配 - MATLAB: [27 36], C++: [33 25]
  state_Szz11: 维度不匹配 - MATLAB: [28 37], C++: [34 26]
  state_Szz22: 维度不匹配 - MATLAB: [27 36], C++: [33 25]
  state_U12: 维度不匹配 - MATLAB: [28 36], C++: [34 25]
  state_U21: 维度不匹配 - MATLAB: [27 37], C++: [33 26]

=== domain_3 ===
参数对比:
  Nx1: MATLAB=20, C++=6 ❌
  Nz1: MATLAB=20, C++=33 ❌
  x_min: MATLAB=-5.015879e+05, C++=-4.242641e+05, 相对差异=15.42%
  x_max: MATLAB=-4.160238e-25, C++=5.119694e-13, 相对差异=123062525517086.28%
  z_min: MATLAB=-5.015879e+05, C++=-5.015879e+05, 相对差异=0.00%
  z_max: MATLAB=3.233068e-11, C++=0.000000e+00, 相对差异=100.00%

矩阵对比:
  dxpdx11: 维度不匹配 - MATLAB: [20 20], C++: [6 33]
  dxpdx22: 维度不匹配 - MATLAB: [19 19], C++: [5 32]
  dxpdz11: 维度不匹配 - MATLAB: [20 20], C++: [6 33]
  dxpdz22: 维度不匹配 - MATLAB: [19 19], C++: [5 32]
  dzpdx11: 维度不匹配 - MATLAB: [20 20], C++: [6 33]
  dzpdx22: 维度不匹配 - MATLAB: [19 19], C++: [5 32]
  dzpdz11: 维度不匹配 - MATLAB: [20 20], C++: [6 33]
  dzpdz22: 维度不匹配 - MATLAB: [19 19], C++: [5 32]
  bxT1: 维度不匹配 - MATLAB: [20 20], C++: [6 6]
  bxT2: 维度不匹配 - MATLAB: [20 19], C++: [6 5]
  bzT1: 维度不匹配 - MATLAB: [20 20], C++: [33 33]
  bzT2: 维度不匹配 - MATLAB: [20 19], C++: [33 32]
  Jac11: 维度不匹配 - MATLAB: [20 20], C++: [6 33]
  Jac22: 维度不匹配 - MATLAB: [19 19], C++: [5 32]
  mu11: 🟢 完全相同 - 最大相对差异: 0.00e+00%, 范围: MATLAB[1.51e+10,1.51e+10], C++[1.51e+10,1.51e+10]
  mu22: 🟢 完全相同 - 最大相对差异: 0.00e+00%, 范围: MATLAB[1.51e+10,1.51e+10], C++[1.51e+10,1.51e+10]
  state_Sxx11: 维度不匹配 - MATLAB: [20 20], C++: [6 33]
  state_Sxx22: 维度不匹配 - MATLAB: [19 19], C++: [5 32]
  state_Szz11: 维度不匹配 - MATLAB: [20 20], C++: [6 33]
  state_Szz22: 维度不匹配 - MATLAB: [19 19], C++: [5 32]
  state_U12: 维度不匹配 - MATLAB: [20 19], C++: [6 32]
  state_U21: 维度不匹配 - MATLAB: [19 20], C++: [5 33]

=== domain_4 ===
参数对比:
  Nx1: MATLAB=20, C++=6 ❌
  Nz1: MATLAB=20, C++=33 ❌
  x_min: MATLAB=-4.849601e-11, C++=-4.849601e-11, 相对差异=0.00%
  x_max: MATLAB=5.015879e+05, C++=4.242641e+05, 相对差异=15.42%
  z_min: MATLAB=-5.015879e+05, C++=-5.015879e+05, 相对差异=0.00%
  z_max: MATLAB=2.067952e-25, C++=0.000000e+00, 相对差异=100.00%

矩阵对比:
  dxpdx11: 维度不匹配 - MATLAB: [20 20], C++: [6 33]
  dxpdx22: 维度不匹配 - MATLAB: [19 19], C++: [5 32]
  dxpdz11: 维度不匹配 - MATLAB: [20 20], C++: [6 33]
  dxpdz22: 维度不匹配 - MATLAB: [19 19], C++: [5 32]
  dzpdx11: 维度不匹配 - MATLAB: [20 20], C++: [6 33]
  dzpdx22: 维度不匹配 - MATLAB: [19 19], C++: [5 32]
  dzpdz11: 维度不匹配 - MATLAB: [20 20], C++: [6 33]
  dzpdz22: 维度不匹配 - MATLAB: [19 19], C++: [5 32]
  bxT1: 维度不匹配 - MATLAB: [20 20], C++: [6 6]
  bxT2: 维度不匹配 - MATLAB: [20 19], C++: [6 5]
  bzT1: 维度不匹配 - MATLAB: [20 20], C++: [33 33]
  bzT2: 维度不匹配 - MATLAB: [20 19], C++: [33 32]
  Jac11: 维度不匹配 - MATLAB: [20 20], C++: [6 33]
  Jac22: 维度不匹配 - MATLAB: [19 19], C++: [5 32]
  mu11: 🟢 完全相同 - 最大相对差异: 0.00e+00%, 范围: MATLAB[1.51e+10,1.51e+10], C++[1.51e+10,1.51e+10]
  mu22: 🟢 完全相同 - 最大相对差异: 0.00e+00%, 范围: MATLAB[1.51e+10,1.51e+10], C++[1.51e+10,1.51e+10]
  state_Sxx11: 维度不匹配 - MATLAB: [20 20], C++: [6 33]
  state_Sxx22: 维度不匹配 - MATLAB: [19 19], C++: [5 32]
  state_Szz11: 维度不匹配 - MATLAB: [20 20], C++: [6 33]
  state_Szz22: 维度不匹配 - MATLAB: [19 19], C++: [5 32]
  state_U12: 维度不匹配 - MATLAB: [20 19], C++: [6 32]
  state_U21: 维度不匹配 - MATLAB: [19 20], C++: [5 33]

=== domain_5 ===
参数对比:
  Nx1: MATLAB=28, C++=34 ❌
  Nz1: MATLAB=37, C++=53 ❌
  x_min: MATLAB=4.242641e+05, C++=4.242641e+05, 相对差异=0.00%
  x_max: MATLAB=1.221500e+06, C++=1.221500e+06, 相对差异=0.00%
  z_min: MATLAB=-8.637309e+05, C++=-8.637309e+05, 相对差异=0.00%
  z_max: MATLAB=-6.466135e-11, C++=-6.466135e-11, 相对差异=0.00%

矩阵对比:
  dxpdx11: 维度不匹配 - MATLAB: [28 37], C++: [34 53]
  dxpdx22: 维度不匹配 - MATLAB: [27 36], C++: [33 52]
  dxpdz11: 维度不匹配 - MATLAB: [28 37], C++: [34 53]
  dxpdz22: 维度不匹配 - MATLAB: [27 36], C++: [33 52]
  dzpdx11: 维度不匹配 - MATLAB: [28 37], C++: [34 53]
  dzpdx22: 维度不匹配 - MATLAB: [27 36], C++: [33 52]
  dzpdz11: 维度不匹配 - MATLAB: [28 37], C++: [34 53]
  dzpdz22: 维度不匹配 - MATLAB: [27 36], C++: [33 52]
  bxT1: 维度不匹配 - MATLAB: [28 28], C++: [34 34]
  bxT2: 维度不匹配 - MATLAB: [28 27], C++: [34 33]
  bzT1: 维度不匹配 - MATLAB: [37 37], C++: [53 53]
  bzT2: 维度不匹配 - MATLAB: [37 36], C++: [53 52]
  Jac11: 维度不匹配 - MATLAB: [28 37], C++: [34 53]
  Jac22: 维度不匹配 - MATLAB: [27 36], C++: [33 52]
  mu11: 🟢 完全相同 - 最大相对差异: 0.00e+00%, 范围: MATLAB[1.51e+10,1.51e+10], C++[1.51e+10,1.51e+10]
  mu22: 🟢 完全相同 - 最大相对差异: 0.00e+00%, 范围: MATLAB[1.51e+10,1.51e+10], C++[1.51e+10,1.51e+10]
  state_Sxx11: 维度不匹配 - MATLAB: [28 37], C++: [34 53]
  state_Sxx22: 维度不匹配 - MATLAB: [27 36], C++: [33 52]
  state_Szz11: 维度不匹配 - MATLAB: [28 37], C++: [34 53]
  state_Szz22: 维度不匹配 - MATLAB: [27 36], C++: [33 52]
  state_U12: 维度不匹配 - MATLAB: [28 36], C++: [34 52]
  state_U21: 维度不匹配 - MATLAB: [27 37], C++: [33 53]

=== domain_6 ===
参数对比:
  Nx1: MATLAB=28, C++=34 ❌
  Nz1: MATLAB=37, C++=26 ❌
  x_min: MATLAB=-1.221500e+06, C++=-1.221500e+06, 相对差异=0.00%
  x_max: MATLAB=-4.242641e+05, C++=-4.242641e+05, 相对差异=0.00%
  z_min: MATLAB=3.233068e-11, C++=7.347881e-11, 相对差异=127.27%
  z_max: MATLAB=8.637309e+05, C++=8.637309e+05, 相对差异=0.00%

矩阵对比:
  dxpdx11: 维度不匹配 - MATLAB: [28 37], C++: [34 26]
  dxpdx22: 维度不匹配 - MATLAB: [27 36], C++: [33 25]
  dxpdz11: 维度不匹配 - MATLAB: [28 37], C++: [34 26]
  dxpdz22: 维度不匹配 - MATLAB: [27 36], C++: [33 25]
  dzpdx11: 维度不匹配 - MATLAB: [28 37], C++: [34 26]
  dzpdx22: 维度不匹配 - MATLAB: [27 36], C++: [33 25]
  dzpdz11: 维度不匹配 - MATLAB: [28 37], C++: [34 26]
  dzpdz22: 维度不匹配 - MATLAB: [27 36], C++: [33 25]
  bxT1: 维度不匹配 - MATLAB: [28 28], C++: [34 34]
  bxT2: 维度不匹配 - MATLAB: [28 27], C++: [34 33]
  bzT1: 维度不匹配 - MATLAB: [37 37], C++: [26 26]
  bzT2: 维度不匹配 - MATLAB: [37 36], C++: [26 25]
  Jac11: 维度不匹配 - MATLAB: [28 37], C++: [34 26]
  Jac22: 维度不匹配 - MATLAB: [27 36], C++: [33 25]
  mu11: 🟢 完全相同 - 最大相对差异: 0.00e+00%, 范围: MATLAB[1.51e+10,1.51e+10], C++[1.51e+10,1.51e+10]
  mu22: 🟢 完全相同 - 最大相对差异: 0.00e+00%, 范围: MATLAB[1.51e+10,1.51e+10], C++[1.51e+10,1.51e+10]
  state_Sxx11: 维度不匹配 - MATLAB: [28 37], C++: [34 26]
  state_Sxx22: 维度不匹配 - MATLAB: [27 36], C++: [33 25]
  state_Szz11: 维度不匹配 - MATLAB: [28 37], C++: [34 26]
  state_Szz22: 维度不匹配 - MATLAB: [27 36], C++: [33 25]
  state_U12: 维度不匹配 - MATLAB: [28 36], C++: [34 25]
  state_U21: 维度不匹配 - MATLAB: [27 37], C++: [33 26]

=== domain_7 ===
参数对比:
  Nx1: MATLAB=20, C++=33 ❌
  Nz1: MATLAB=20, C++=6 ❌
  x_min: MATLAB=-5.015879e+05, C++=-5.015879e+05, 相对差异=0.00%
  x_max: MATLAB=1.616534e-11, C++=0.000000e+00, 相对差异=100.00%
  z_min: MATLAB=-4.135903e-25, C++=-3.413129e-13, 相对差异=82524405799187.42%
  z_max: MATLAB=5.015879e+05, C++=4.242641e+05, 相对差异=15.42%

矩阵对比:
  dxpdx11: 维度不匹配 - MATLAB: [20 20], C++: [33 6]
  dxpdx22: 维度不匹配 - MATLAB: [19 19], C++: [32 5]
  dxpdz11: 维度不匹配 - MATLAB: [20 20], C++: [33 6]
  dxpdz22: 维度不匹配 - MATLAB: [19 19], C++: [32 5]
  dzpdx11: 维度不匹配 - MATLAB: [20 20], C++: [33 6]
  dzpdx22: 维度不匹配 - MATLAB: [19 19], C++: [32 5]
  dzpdz11: 维度不匹配 - MATLAB: [20 20], C++: [33 6]
  dzpdz22: 维度不匹配 - MATLAB: [19 19], C++: [32 5]
  bxT1: 维度不匹配 - MATLAB: [20 20], C++: [33 33]
  bxT2: 维度不匹配 - MATLAB: [20 19], C++: [33 32]
  bzT1: 维度不匹配 - MATLAB: [20 20], C++: [6 6]
  bzT2: 维度不匹配 - MATLAB: [20 19], C++: [6 5]
  Jac11: 维度不匹配 - MATLAB: [20 20], C++: [33 6]
  Jac22: 维度不匹配 - MATLAB: [19 19], C++: [32 5]
  mu11: 🟢 完全相同 - 最大相对差异: 0.00e+00%, 范围: MATLAB[1.51e+10,1.51e+10], C++[1.51e+10,1.51e+10]
  mu22: 🟢 完全相同 - 最大相对差异: 0.00e+00%, 范围: MATLAB[1.51e+10,1.51e+10], C++[1.51e+10,1.51e+10]
  state_Sxx11: 维度不匹配 - MATLAB: [20 20], C++: [33 6]
  state_Sxx22: 维度不匹配 - MATLAB: [19 19], C++: [32 5]
  state_Szz11: 维度不匹配 - MATLAB: [20 20], C++: [33 6]
  state_Szz22: 维度不匹配 - MATLAB: [19 19], C++: [32 5]
  state_U12: 维度不匹配 - MATLAB: [20 19], C++: [33 5]
  state_U21: 维度不匹配 - MATLAB: [19 20], C++: [32 6]

=== domain_8 ===
参数对比:
  Nx1: MATLAB=20, C++=33 ❌
  Nz1: MATLAB=20, C++=33 ❌
  x_min: MATLAB=-2.395823e-25, C++=0.000000e+00, 相对差异=100.00%
  x_max: MATLAB=5.015879e+05, C++=5.015879e+05, 相对差异=0.00%
  z_min: MATLAB=-4.135903e-25, C++=0.000000e+00, 相对差异=100.00%
  z_max: MATLAB=5.015879e+05, C++=5.015879e+05, 相对差异=0.00%

矩阵对比:
  dxpdx11: 维度不匹配 - MATLAB: [20 20], C++: [33 33]
  dxpdx22: 维度不匹配 - MATLAB: [19 19], C++: [32 32]
  dxpdz11: 维度不匹配 - MATLAB: [20 20], C++: [33 33]
  dxpdz22: 维度不匹配 - MATLAB: [19 19], C++: [32 32]
  dzpdx11: 维度不匹配 - MATLAB: [20 20], C++: [33 33]
  dzpdx22: 维度不匹配 - MATLAB: [19 19], C++: [32 32]
  dzpdz11: 维度不匹配 - MATLAB: [20 20], C++: [33 33]
  dzpdz22: 维度不匹配 - MATLAB: [19 19], C++: [32 32]
  bxT1: 维度不匹配 - MATLAB: [20 20], C++: [33 33]
  bxT2: 维度不匹配 - MATLAB: [20 19], C++: [33 32]
  bzT1: 维度不匹配 - MATLAB: [20 20], C++: [33 33]
  bzT2: 维度不匹配 - MATLAB: [20 19], C++: [33 32]
  Jac11: 维度不匹配 - MATLAB: [20 20], C++: [33 33]
  Jac22: 维度不匹配 - MATLAB: [19 19], C++: [32 32]
  mu11: 🟢 完全相同 - 最大相对差异: 0.00e+00%, 范围: MATLAB[1.51e+10,1.51e+10], C++[1.51e+10,1.51e+10]
  mu22: 🟢 完全相同 - 最大相对差异: 0.00e+00%, 范围: MATLAB[1.51e+10,1.51e+10], C++[1.51e+10,1.51e+10]
  state_Sxx11: 维度不匹配 - MATLAB: [20 20], C++: [33 33]
  state_Sxx22: 维度不匹配 - MATLAB: [19 19], C++: [32 32]
  state_Szz11: 维度不匹配 - MATLAB: [20 20], C++: [33 33]
  state_Szz22: 维度不匹配 - MATLAB: [19 19], C++: [32 32]
  state_U12: 维度不匹配 - MATLAB: [20 19], C++: [33 32]
  state_U21: 维度不匹配 - MATLAB: [19 20], C++: [32 33]

=== domain_9 ===
参数对比:
  Nx1: MATLAB=28, C++=34 ❌
  Nz1: MATLAB=37, C++=53 ❌
  x_min: MATLAB=4.242641e+05, C++=4.242641e+05, 相对差异=0.00%
  x_max: MATLAB=1.221500e+06, C++=1.221500e+06, 相对差异=0.00%
  z_min: MATLAB=-2.991812e-10, C++=-2.991812e-10, 相对差异=0.00%
  z_max: MATLAB=8.637309e+05, C++=8.637309e+05, 相对差异=0.00%

矩阵对比:
  dxpdx11: 维度不匹配 - MATLAB: [28 37], C++: [34 53]
  dxpdx22: 维度不匹配 - MATLAB: [27 36], C++: [33 52]
  dxpdz11: 维度不匹配 - MATLAB: [28 37], C++: [34 53]
  dxpdz22: 维度不匹配 - MATLAB: [27 36], C++: [33 52]
  dzpdx11: 维度不匹配 - MATLAB: [28 37], C++: [34 53]
  dzpdx22: 维度不匹配 - MATLAB: [27 36], C++: [33 52]
  dzpdz11: 维度不匹配 - MATLAB: [28 37], C++: [34 53]
  dzpdz22: 维度不匹配 - MATLAB: [27 36], C++: [33 52]
  bxT1: 维度不匹配 - MATLAB: [28 28], C++: [34 34]
  bxT2: 维度不匹配 - MATLAB: [28 27], C++: [34 33]
  bzT1: 维度不匹配 - MATLAB: [37 37], C++: [53 53]
  bzT2: 维度不匹配 - MATLAB: [37 36], C++: [53 52]
  Jac11: 维度不匹配 - MATLAB: [28 37], C++: [34 53]
  Jac22: 维度不匹配 - MATLAB: [27 36], C++: [33 52]
  mu11: 🟢 完全相同 - 最大相对差异: 0.00e+00%, 范围: MATLAB[1.51e+10,1.51e+10], C++[1.51e+10,1.51e+10]
  mu22: 🟢 完全相同 - 最大相对差异: 0.00e+00%, 范围: MATLAB[1.51e+10,1.51e+10], C++[1.51e+10,1.51e+10]
  state_Sxx11: 维度不匹配 - MATLAB: [28 37], C++: [34 53]
  state_Sxx22: 维度不匹配 - MATLAB: [27 36], C++: [33 52]
  state_Szz11: 维度不匹配 - MATLAB: [28 37], C++: [34 53]
  state_Szz22: 维度不匹配 - MATLAB: [27 36], C++: [33 52]
  state_U12: 维度不匹配 - MATLAB: [28 36], C++: [34 52]
  state_U21: 维度不匹配 - MATLAB: [27 37], C++: [33 53]

=== domain_10 ===
参数对比:
  Nx1: MATLAB=37, C++=53 ❌
  Nz1: MATLAB=28, C++=34 ❌
  x_min: MATLAB=-8.637309e+05, C++=-8.637309e+05, 相对差异=0.00%
  x_max: MATLAB=7.479530e-11, C++=7.479530e-11, 相对差异=0.00%
  z_min: MATLAB=4.242641e+05, C++=4.242641e+05, 相对差异=0.00%
  z_max: MATLAB=1.221500e+06, C++=1.221500e+06, 相对差异=0.00%

矩阵对比:
  dxpdx11: 维度不匹配 - MATLAB: [37 28], C++: [53 34]
  dxpdx22: 维度不匹配 - MATLAB: [36 27], C++: [52 33]
  dxpdz11: 维度不匹配 - MATLAB: [37 28], C++: [53 34]
  dxpdz22: 维度不匹配 - MATLAB: [36 27], C++: [52 33]
  dzpdx11: 维度不匹配 - MATLAB: [37 28], C++: [53 34]
  dzpdx22: 维度不匹配 - MATLAB: [36 27], C++: [52 33]
  dzpdz11: 维度不匹配 - MATLAB: [37 28], C++: [53 34]
  dzpdz22: 维度不匹配 - MATLAB: [36 27], C++: [52 33]
  bxT1: 维度不匹配 - MATLAB: [37 37], C++: [53 53]
  bxT2: 维度不匹配 - MATLAB: [37 36], C++: [53 52]
  bzT1: 维度不匹配 - MATLAB: [28 28], C++: [34 34]
  bzT2: 维度不匹配 - MATLAB: [28 27], C++: [34 33]
  Jac11: 维度不匹配 - MATLAB: [37 28], C++: [53 34]
  Jac22: 维度不匹配 - MATLAB: [36 27], C++: [52 33]
  mu11: 🟢 完全相同 - 最大相对差异: 0.00e+00%, 范围: MATLAB[1.51e+10,1.51e+10], C++[1.51e+10,1.51e+10]
  mu22: 🟢 完全相同 - 最大相对差异: 0.00e+00%, 范围: MATLAB[1.51e+10,1.51e+10], C++[1.51e+10,1.51e+10]
  state_Sxx11: 维度不匹配 - MATLAB: [37 28], C++: [53 34]
  state_Sxx22: 维度不匹配 - MATLAB: [36 27], C++: [52 33]
  state_Szz11: 维度不匹配 - MATLAB: [37 28], C++: [53 34]
  state_Szz22: 维度不匹配 - MATLAB: [36 27], C++: [52 33]
  state_U12: 维度不匹配 - MATLAB: [37 27], C++: [53 33]
  state_U21: 维度不匹配 - MATLAB: [36 28], C++: [52 34]

=== domain_11 ===
参数对比:
  Nx1: MATLAB=37, C++=53 ❌
  Nz1: MATLAB=28, C++=34 ❌
  x_min: MATLAB=1.616534e-11, C++=1.616534e-11, 相对差异=0.00%
  x_max: MATLAB=8.637309e+05, C++=8.637309e+05, 相对差异=0.00%
  z_min: MATLAB=4.242641e+05, C++=4.242641e+05, 相对差异=0.00%
  z_max: MATLAB=1.221500e+06, C++=1.221500e+06, 相对差异=0.00%

矩阵对比:
  dxpdx11: 维度不匹配 - MATLAB: [37 28], C++: [53 34]
  dxpdx22: 维度不匹配 - MATLAB: [36 27], C++: [52 33]
  dxpdz11: 维度不匹配 - MATLAB: [37 28], C++: [53 34]
  dxpdz22: 维度不匹配 - MATLAB: [36 27], C++: [52 33]
  dzpdx11: 维度不匹配 - MATLAB: [37 28], C++: [53 34]
  dzpdx22: 维度不匹配 - MATLAB: [36 27], C++: [52 33]
  dzpdz11: 维度不匹配 - MATLAB: [37 28], C++: [53 34]
  dzpdz22: 维度不匹配 - MATLAB: [36 27], C++: [52 33]
  bxT1: 维度不匹配 - MATLAB: [37 37], C++: [53 53]
  bxT2: 维度不匹配 - MATLAB: [37 36], C++: [53 52]
  bzT1: 维度不匹配 - MATLAB: [28 28], C++: [34 34]
  bzT2: 维度不匹配 - MATLAB: [28 27], C++: [34 33]
  Jac11: 维度不匹配 - MATLAB: [37 28], C++: [53 34]
  Jac22: 维度不匹配 - MATLAB: [36 27], C++: [52 33]
  mu11: 🟢 完全相同 - 最大相对差异: 0.00e+00%, 范围: MATLAB[1.51e+10,1.51e+10], C++[1.51e+10,1.51e+10]
  mu22: 🟢 完全相同 - 最大相对差异: 0.00e+00%, 范围: MATLAB[1.51e+10,1.51e+10], C++[1.51e+10,1.51e+10]
  state_Sxx11: 维度不匹配 - MATLAB: [37 28], C++: [53 34]
  state_Sxx22: 维度不匹配 - MATLAB: [36 27], C++: [52 33]
  state_Szz11: 维度不匹配 - MATLAB: [37 28], C++: [53 34]
  state_Szz22: 维度不匹配 - MATLAB: [36 27], C++: [52 33]
  state_U12: 维度不匹配 - MATLAB: [37 27], C++: [53 33]
  state_U21: 维度不匹配 - MATLAB: [36 28], C++: [52 34]

=== 最终统计 ===
总矩阵数量: 264
完全相同: 24 (9.1%)
优秀匹配: 0 (0.0%)
良好匹配: 0 (0.0%)
可接受匹配: 0 (0.0%)
匹配较差: 0 (0.0%)
维度不匹配: 240 (90.9%)
错误: 0 (0.0%)
