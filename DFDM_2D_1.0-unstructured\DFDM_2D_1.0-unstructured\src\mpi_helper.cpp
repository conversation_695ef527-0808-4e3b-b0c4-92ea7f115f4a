#include "mpi_helper.hpp"
#include <cmath>
void DFDM::mpi_init(){
    MPI_Init(NULL, NULL);
}

void DFDM::mpi_finalize(){
    MPI_Finalize();
}

int DFDM::get_rank_id(){
    int rank;
    MPI_Comm_rank(MPI_COMM_WORLD, &rank);

    return rank;
}

int DFDM::get_total_ranks(){
    int comm_size;
    MPI_Comm_size(MPI_COMM_WORLD, &comm_size);

    return comm_size;
}

void DFDM::recv_mat(int src_rank, int32_t tag, DFDM::matrix<double>& mat){ // if an element is receiving, it will provide its id as tag
  std::vector<uint32_t> dims(2);
  uint32_t rec_tag_dim = tag;
  uint32_t rec_tag_data = tag ^ (1<< 22);

  if (rec_tag_data > maxtagsizeofi){
    std::cout << "MPI::ERROR::TAG SIZE larger than allowable on OFI with SS-11" << std::endl;
    exit(-1);
  }

  MPI_Recv((void*)dims.data(), dims.size(), MPI_INT, src_rank, rec_tag_dim, MPI_COMM_WORLD, MPI_STATUS_IGNORE);
  mat.rows = dims[0];
  mat.cols = dims[1];
  mat.resize(mat.rows, mat.cols);
  MPI_Recv((void*)mat.data_vec.data(), mat.data_vec.size(), MPI_DOUBLE, src_rank, rec_tag_data, MPI_COMM_WORLD, MPI_STATUS_IGNORE);
}
//non blocking receive
void DFDM::recv_mat_nb(int src_rank, int32_t tag, DFDM::matrix<double>& mat, MPI_Request& request_1, MPI_Request& request_2){ // if an element is receiving, it will provide its id as tag
  std::vector<uint32_t> dims(2);  
  
  uint32_t rec_tag_dim = tag;
  uint32_t rec_tag_data = tag ^ (1<< 22);

  if (rec_tag_data > maxtagsizeofi){
    std::cout << "MPI::ERROR::TAG SIZE larger than allowable on OFI with SS-11" << std::endl;
    exit(-1);
  }

  MPI_Irecv((void*)dims.data(), dims.size(), MPI_INT, src_rank, rec_tag_dim, MPI_COMM_WORLD, &request_1);
  MPI_Wait(&request_1,MPI_STATUS_IGNORE);
  mat.rows = dims[0];
  mat.cols = dims[1];
  mat.resize(mat.rows, mat.cols);
  MPI_Irecv((void*)mat.data_vec.data(), mat.data_vec.size(), MPI_DOUBLE, src_rank, rec_tag_data, MPI_COMM_WORLD, &request_2);
}

void DFDM::send_mat(int target_rank, int32_t tag, DFDM::matrix<double>& mat){ // if an element is sending, it will provide the destination element id as tag
  std::vector<uint32_t> dims(2);
  dims[0] = mat.rows;
  dims[1] = mat.cols;
  int32_t rec_tag_dim = tag;
  int32_t rec_tag_data = tag ^ (1<< 22);

  if (rec_tag_data > maxtagsizeofi){
    std::cout << "MPI::ERROR::TAG SIZE larger than allowable on OFI with SS-11" << std::endl;
    exit(-1);
  }

  MPI_Send((void*)dims.data(), dims.size(), MPI_INT, target_rank, rec_tag_dim, MPI_COMM_WORLD); // using my element_id as a tag, receiver will use target id
  MPI_Send((void*)mat.data_vec.data(), mat.data_vec.size(), MPI_DOUBLE, target_rank, rec_tag_data, MPI_COMM_WORLD);
}

//non blocking send
void DFDM::send_mat_nb(int target_rank, int32_t tag, DFDM::matrix<double>& mat, MPI_Request& request_1, MPI_Request& request_2){ // if an element is sending, it will provide the destination element id as tag
  std::vector<uint32_t> dims(2);
  dims[0] = mat.rows;
  dims[1] = mat.cols;
  int32_t rec_tag_dim = tag;
  int32_t rec_tag_data = tag ^ (1<< 22);
  MPI_Isend((void*)dims.data(), dims.size(), MPI_INT, target_rank, rec_tag_dim, MPI_COMM_WORLD, &request_1); // when sending, using target element id as tag, receiver will use my element id as tag
  MPI_Isend((void*)mat.data_vec.data(), mat.data_vec.size(), MPI_DOUBLE, target_rank, rec_tag_data, MPI_COMM_WORLD, &request_2);
}

void DFDM::mpi_wait_all(uint32_t ccount, std::vector<MPI_Request>& request, std::vector<MPI_Status>& status){
  switch(ccount){
    case 1:
      MPI_Wait(&request[1], MPI_STATUS_IGNORE);
      break;
    case 2:
      MPI_Wait(&request[3], MPI_STATUS_IGNORE);
      break;
    case 3:
      MPI_Wait(&request[1], MPI_STATUS_IGNORE);
      MPI_Wait(&request[3], MPI_STATUS_IGNORE);
      break;
    default:
      break;
  }  
}

// Function to wrap MPI_Waitall with MPI_STATUS_IGNORE
void DFDM::mpi_wait(MPI_Request& request1, MPI_Request& request2){
    int err1 = MPI_Wait(&request1, MPI_STATUS_IGNORE);   
    if (err1!= MPI_SUCCESS) {
        std::cerr << "MPI_Waitall failed with error code " << err1 << std::endl;
        exit(-1);
    }

    int err2 = MPI_Wait(&request2, MPI_STATUS_IGNORE);
    
    if (err2!= MPI_SUCCESS) {
        std::cerr << "MPI_Waitall failed with error code " << err2 << std::endl;
        exit(-1);
    }
}

int32_t DFDM::generateTag(BdryType boundaryType, BdryDirection boundaryDirection, uint32_t procId, uint32_t eleid){
  if (boundaryType > 15 || boundaryDirection > 4 || procId > 255 || eleid > 255){
    std::cerr << "generateTag: Invalid boundary type, direction, procId, or eleid. "
              << std::endl;
    return -1;
  }

  int32_t tag = ((boundaryType << 20) & TAG_DIR_MASK) | 
         ((boundaryDirection << 16) & TAG_TYPE_MASK) | 
         ((procId << 8) & TAG_PROC_MASK) | 
         (eleid & TAG_ELE_MASK);


  if (tag > maxtagsizeofi){
    std::cout << "MPI::ERROR::TAG SIZE larger than allowable on OFI with SS-11" << std::endl;
    exit(-1);
  }

  return tag;
}
