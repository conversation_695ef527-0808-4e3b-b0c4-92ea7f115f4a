#include "simulation.hpp"


DFDM::simulation::simulation()
                            : Vp(0.0), rho(2000.0), frequency(1), duration(10)
                            , order_b2(4), order_b1(5),gauss_order(5), delta_t(0.001), source_elem_id(0)
                            , receiver_elem_id(0), time_steps(40000)
                            {}

DFDM::simulation::simulation(std::string toml_file){ // reads from toml file and process data (per CPU domain)
    auto data = toml::parse(toml_file);
    Vp = toml::find<double>(data, "Vp");
    Vmax = toml::find<double>(data, "Vmax");

    rho = toml::find<double>(data, "rho");
    // grid_length_x = toml::find<double>(data, "grid_size_x");
    // grid_length_z = toml::find<double>(data, "grid_size_z");
    frequency = toml::find<double>(data, "frequency");
    duration = toml::find<double>(data, "duration");

    order_b2 = toml::find<uint32_t>(data, "order_b2");
    order_b1 = toml::find<uint32_t>(data, "order_b1");

    gauss_order = toml::find<uint32_t>(data, "gauss_order");

    delta_t = toml::find<double>(data, "delta_t");
    time_steps = toml::find<uint64_t>(data, "time_steps");

    source_elem_id = toml::find<uint32_t>(data, "source_element");
    receiver_elem_id = toml::find<uint32_t>(data, "receiver_element");

    auto my_rank = DFDM::get_rank_id();
    if(my_rank == 0){
        std::cout << "DFDM Simulation Configs:" << std::endl;
        std::cout << "\tvelocity: "<<Vp<< std::endl;
        std::cout << "\tfrequency: "<< frequency<< std::endl;
        std::cout << "\torder_b2: "<< order_b2<< std::endl;
        std::cout << "\torder_b1: "<< order_b1<< std::endl;
        std::cout << "\tgauss_order: "<<gauss_order<< std::endl;
        std::cout << "\tsource_element: "<< source_elem_id<< std::endl;
        std::cout << "\treceiver_element: "<< receiver_elem_id<< std::endl;
        std::cout << "\trho: "<< rho<< std::endl;
        std::cout << "\tdelta_t: "<< delta_t<< std::endl;
        std::cout << "\ttime_steps: "<< time_steps<< std::endl;

    }
   

}




