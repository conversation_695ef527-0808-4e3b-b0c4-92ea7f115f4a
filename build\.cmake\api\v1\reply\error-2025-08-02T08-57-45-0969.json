{"cmake": {"generator": {"multiConfig": true, "name": "Visual Studio 17 2022", "platform": "win32"}, "paths": {"cmake": "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/bin/cmake.exe", "cpack": "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/bin/cpack.exe", "ctest": "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/bin/ctest.exe", "root": "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/share/cmake-4.1"}, "version": {"isDirty": false, "major": 4, "minor": 1, "patch": 0, "string": "4.1.0-rc4", "suffix": "rc4"}}, "objects": [], "reply": {"client-vscode": {"query.json": {"requests": [{"kind": "cache", "version": 2}, {"kind": "codemodel", "version": 2}, {"kind": "toolchains", "version": 1}, {"kind": "cmakeFiles", "version": 1}], "responses": [{"error": "no buildsystem generated"}, {"error": "no buildsystem generated"}, {"error": "no buildsystem generated"}, {"error": "no buildsystem generated"}]}}}}