function extract_comparison_data()
%% MATLAB vs C++ Data Comparison Script
% Compare MATLAB workspace data with C++ output data from multiple domains
% Author: AI Assistant
% Date: 2024

clc;
fprintf('=== MATLAB vs C++ 数据对比脚本 ===\n');

% Load OM data from om.mat file
om_file = 'om.mat';
if exist(om_file, 'file')
    fprintf('正在从 %s 加载 OM 数据...\n', om_file);
    load_data = load(om_file);
    if isfield(load_data, 'OM')
        OM = load_data.OM;
        fprintf('成功从 %s 获取 OM 数据\n', om_file);
    else
        error('om.mat 文件中没有 OM 变量');
    end
else
    error('找不到 om.mat 文件，请确保文件存在于当前目录');
end

% Create output directory
output_dir = 'output/matlab_vs_cpp_comparison';
if ~exist(output_dir, 'dir')
    mkdir(output_dir);
end

% C++ output directory
cpp_base_dir = 'D:\\project\\ware\\output_cpp';

% Get available C++ domains
cpp_domains = get_cpp_domains(cpp_base_dir);
fprintf('发现 %d 个 C++ 域: %s\n', length(cpp_domains), strjoin(cpp_domains, ', '));

% Get MATLAB domains count
matlab_domain_count = length(OM);
fprintf('MATLAB 域数量: %d\n', matlab_domain_count);

%% 1. Save MATLAB data for all domains
fprintf('\n=== 保存 MATLAB 数据 ===\n');
save_matlab_data(OM, output_dir);

%% 2. Compare data between MATLAB and C++
fprintf('\n=== 开始数据对比 ===\n');
comparison_results = compare_all_domains(OM, cpp_base_dir, output_dir);

%% 3. Generate comparison report
fprintf('\n=== 生成对比报告 ===\n');
generate_comparison_report(comparison_results, output_dir);

fprintf('\n✅ 数据对比完成！结果保存在: %s\n', output_dir);
fprintf('=== 对比脚本执行完毕 ===\n');

end

%% Helper Functions

function cpp_domains = get_cpp_domains(cpp_base_dir)
% Get list of available C++ domain directories
    cpp_domains = {};
    if exist(cpp_base_dir, 'dir')
        dir_list = dir(fullfile(cpp_base_dir, 'domain_*'));
        for i = 1:length(dir_list)
            if dir_list(i).isdir
                cpp_domains{end+1} = dir_list(i).name;
            end
        end
        % Sort domains numerically
        domain_nums = cellfun(@(x) str2double(x(8:end)), cpp_domains);
        [~, sort_idx] = sort(domain_nums);
        cpp_domains = cpp_domains(sort_idx);
    end
end

function save_matlab_data(OM, output_dir)
% Save MATLAB data for all domains
    matlab_dir = fullfile(output_dir, 'matlab_data');
    if ~exist(matlab_dir, 'dir')
        mkdir(matlab_dir);
    end
    
    for domain_idx = 1:length(OM)
        domain = OM(domain_idx);
        domain_dir = fullfile(matlab_dir, sprintf('domain_%d', domain_idx-1));
        if ~exist(domain_dir, 'dir')
            mkdir(domain_dir);
        end
        
        fprintf('  保存 MATLAB 域 %d 数据...\n', domain_idx-1);
        
        % Save parameters
        save_domain_parameters(domain, domain_idx-1, domain_dir);
        
        % Save matrices
        save_domain_matrices(domain, domain_dir);
    end
end

function save_domain_parameters(domain, domain_idx, output_dir)
% Save domain parameters to text file
    params_file = fullfile(output_dir, 'parameters.txt');
    fid = fopen(params_file, 'w');
    
    fprintf(fid, '# MATLAB Domain %d Parameters\n', domain_idx);
    fprintf(fid, 'iom = %d\n', domain_idx);
    
    % Basic dimensions
    if isfield(domain, 'Nx1') && ~isempty(domain.Nx1)
        fprintf(fid, 'Nx1 = %d\n', domain.Nx1);
        fprintf(fid, 'Nx2 = %d\n', domain.Nx1 - 1);
    end
    if isfield(domain, 'Nz1') && ~isempty(domain.Nz1)
        fprintf(fid, 'Nz1 = %d\n', domain.Nz1);
        fprintf(fid, 'Nz2 = %d\n', domain.Nz1 - 1);
    end
    if isfield(domain, 'px1') && ~isempty(domain.px1)
        fprintf(fid, 'px1 = %d\n', domain.px1);
    end
    if isfield(domain, 'pz1') && ~isempty(domain.pz1)
        fprintf(fid, 'pz1 = %d\n', domain.pz1);
    end
    
    % Domain boundaries
    if isfield(domain, 'x2d11') && ~isempty(domain.x2d11)
        x_min = min(domain.x2d11(:));
        x_max = max(domain.x2d11(:));
        z_min = min(domain.z2d11(:));
        z_max = max(domain.z2d11(:));
        fprintf(fid, 'x_min = %.16e\n', x_min);
        fprintf(fid, 'x_max = %.16e\n', x_max);
        fprintf(fid, 'z_min = %.16e\n', z_min);
        fprintf(fid, 'z_max = %.16e\n', z_max);
    end
    
    % Material properties
    if isfield(domain, 'mu11') && ~isempty(domain.mu11)
        fprintf(fid, 'mu = %.16e\n', domain.mu11(1,1));
    end
    if isfield(domain, 'rho11') && ~isempty(domain.rho11)
        fprintf(fid, 'rho = %.16e\n', domain.rho11(1,1));
    end
    
    fclose(fid);
end

function save_domain_matrices(domain, output_dir)
% Save all matrices for a domain
    % Define matrix fields to save
    matrix_fields = {
        'bxT1', 'bxT2', 'bzT1', 'bzT2', ...
        'dxpdx11', 'dxpdx22', 'dxpdz11', 'dxpdz22', ...
        'dzpdx11', 'dzpdx22', 'dzpdz11', 'dzpdz22', ...
        'invLx11', 'invLx22', 'invLxT11', 'invLxT22', ...
        'invLz11', 'invLz22', 'invLzT11', 'invLzT22', ...
        'Jac11', 'Jac22', 'kkx12', 'kkx21', 'kkz12', 'kkz21', ...
        'mu11', 'mu22'
    };
    
    % Save matrices
    for i = 1:length(matrix_fields)
        field = matrix_fields{i};
        if isfield(domain, field) && ~isempty(domain.(field))
            filename = fullfile(output_dir, [field '.txt']);
            save_matrix_to_file(domain.(field), filename);
        end
    end
    
    % Save state matrices if they exist
    if isfield(domain, 'state')
        state_fields = {'Sxx11', 'Sxx22', 'Szz11', 'Szz22', 'U12', 'U21'};
        for i = 1:length(state_fields)
            field = state_fields{i};
            if isfield(domain.state, field) && ~isempty(domain.state.(field))
                filename = fullfile(output_dir, ['state_' field '.txt']);
                save_matrix_to_file(domain.state.(field), filename);
            end
        end
        
        % Save boundary vectors
        vector_fields = {'U12mo', 'U12om', 'U12op', 'U12po', ...
                        'U21mo', 'U21om', 'U21op', 'U21po'};
        for i = 1:length(vector_fields)
            field = vector_fields{i};
            if isfield(domain.state, field) && ~isempty(domain.state.(field))
                filename = fullfile(output_dir, ['state_' field '.txt']);
                save_vector_to_file(domain.state.(field), filename);
            end
        end
    end
end

function save_matrix_to_file(matrix, filename)
% Save matrix to file in the same format as C++
    fid = fopen(filename, 'w');
    [rows, cols] = size(matrix);
    for i = 1:rows
        for j = 1:cols
            if j == cols
                fprintf(fid, '%.16e\n', matrix(i,j));
            else
                fprintf(fid, '%.16e ', matrix(i,j));
            end
        end
    end
    fclose(fid);
end

function save_vector_to_file(vector, filename)
% Save vector to file
    fid = fopen(filename, 'w');
    for i = 1:length(vector)
        fprintf(fid, '%.16e\n', vector(i));
    end
    fclose(fid);
end

function comparison_results = compare_all_domains(OM, cpp_base_dir, output_dir)
% Compare MATLAB and C++ data for all domains
    comparison_results = struct();
    
    % Get available C++ domains
    cpp_domains = get_cpp_domains(cpp_base_dir);
    
    for i = 1:length(cpp_domains)
        domain_name = cpp_domains{i};
        domain_idx = str2double(domain_name(8:end)) + 1; % Convert to 1-based index
        
        fprintf('  对比域 %d...\n', domain_idx-1);
        
        if domain_idx <= length(OM)
            matlab_domain = OM(domain_idx);
            cpp_domain_dir = fullfile(cpp_base_dir, domain_name);
            
            % Compare this domain
            domain_comparison = compare_single_domain(matlab_domain, cpp_domain_dir, domain_idx-1);
            comparison_results.(domain_name) = domain_comparison;
        else
            fprintf('    警告: MATLAB 中没有域 %d\n', domain_idx-1);
            comparison_results.(domain_name) = struct('error', 'MATLAB domain not found');
        end
    end
end

function domain_comparison = compare_single_domain(matlab_domain, cpp_domain_dir, domain_idx)
% Compare a single domain between MATLAB and C++
    domain_comparison = struct();
    domain_comparison.domain_idx = domain_idx;
    domain_comparison.matrices = struct();
    domain_comparison.parameters = struct();
    
    % Compare parameters
    cpp_params_file = fullfile(cpp_domain_dir, 'parameters.txt');
    if exist(cpp_params_file, 'file')
        domain_comparison.parameters = compare_parameters(matlab_domain, cpp_params_file);
    end
    
    % Define matrices to compare
    matrix_fields = {
        'dxpdx11', 'dxpdx22', 'dxpdz11', 'dxpdz22', ...
        'dzpdx11', 'dzpdx22', 'dzpdz11', 'dzpdz22', ...
        'bxT1', 'bxT2', 'bzT1', 'bzT2', ...
        'Jac11', 'Jac22', 'mu11', 'mu22'
    };
    
    % Compare matrices
    for i = 1:length(matrix_fields)
        field = matrix_fields{i};
        cpp_file = fullfile(cpp_domain_dir, [field '.txt']);
        
        if isfield(matlab_domain, field) && ~isempty(matlab_domain.(field)) && exist(cpp_file, 'file')
            try
                matlab_matrix = matlab_domain.(field);
                cpp_matrix = load(cpp_file);
                
                comparison = compare_matrices(matlab_matrix, cpp_matrix, field);
                domain_comparison.matrices.(field) = comparison;
                
            catch ME
                domain_comparison.matrices.(field) = struct('error', ME.message);
            end
        end
    end
    
    % Compare state matrices if they exist
    if isfield(matlab_domain, 'state')
        state_fields = {'Sxx11', 'Sxx22', 'Szz11', 'Szz22', 'U12', 'U21'};
        for i = 1:length(state_fields)
            field = state_fields{i};
            cpp_file = fullfile(cpp_domain_dir, ['state_' field '.txt']);
            
            if isfield(matlab_domain.state, field) && ~isempty(matlab_domain.state.(field)) && exist(cpp_file, 'file')
                try
                    matlab_matrix = matlab_domain.state.(field);
                    cpp_matrix = load(cpp_file);
                    
                    comparison = compare_matrices(matlab_matrix, cpp_matrix, ['state_' field]);
                    domain_comparison.matrices.(['state_' field]) = comparison;
                    
                catch ME
                    domain_comparison.matrices.(['state_' field]) = struct('error', ME.message);
                end
            end
        end
    end
end

function param_comparison = compare_parameters(matlab_domain, cpp_params_file)
% Compare domain parameters
    param_comparison = struct();
    
    % Read C++ parameters
    cpp_params = struct();
    fid = fopen(cpp_params_file, 'r');
    while ~feof(fid)
        line = fgetl(fid);
        if ischar(line) && contains(line, '=') && ~startsWith(line, '#')
            parts = split(line, '=');
            if length(parts) == 2
                key = strtrim(parts{1});
                value = str2double(strtrim(parts{2}));
                if ~isnan(value)
                    cpp_params.(key) = value;
                end
            end
        end
    end
    fclose(fid);
    
    % Compare basic dimensions
    if isfield(matlab_domain, 'Nx1') && isfield(cpp_params, 'Nx1')
        param_comparison.Nx1 = struct('matlab', matlab_domain.Nx1, 'cpp', cpp_params.Nx1, 'match', matlab_domain.Nx1 == cpp_params.Nx1);
    end
    if isfield(matlab_domain, 'Nz1') && isfield(cpp_params, 'Nz1')
        param_comparison.Nz1 = struct('matlab', matlab_domain.Nz1, 'cpp', cpp_params.Nz1, 'match', matlab_domain.Nz1 == cpp_params.Nz1);
    end
    
    % Compare domain boundaries
    if isfield(matlab_domain, 'x2d11') && ~isempty(matlab_domain.x2d11)
        matlab_x_min = min(matlab_domain.x2d11(:));
        matlab_x_max = max(matlab_domain.x2d11(:));
        matlab_z_min = min(matlab_domain.z2d11(:));
        matlab_z_max = max(matlab_domain.z2d11(:));
        
        if isfield(cpp_params, 'x_min')
            param_comparison.x_min = struct('matlab', matlab_x_min, 'cpp', cpp_params.x_min, 'rel_diff', abs(matlab_x_min - cpp_params.x_min) / abs(matlab_x_min) * 100);
        end
        if isfield(cpp_params, 'x_max')
            param_comparison.x_max = struct('matlab', matlab_x_max, 'cpp', cpp_params.x_max, 'rel_diff', abs(matlab_x_max - cpp_params.x_max) / abs(matlab_x_max) * 100);
        end
        if isfield(cpp_params, 'z_min')
            param_comparison.z_min = struct('matlab', matlab_z_min, 'cpp', cpp_params.z_min, 'rel_diff', abs(matlab_z_min - cpp_params.z_min) / abs(matlab_z_min) * 100);
        end
        if isfield(cpp_params, 'z_max')
            param_comparison.z_max = struct('matlab', matlab_z_max, 'cpp', cpp_params.z_max, 'rel_diff', abs(matlab_z_max - cpp_params.z_max) / abs(matlab_z_max) * 100);
        end
    end
end

function comparison = compare_matrices(matlab_matrix, cpp_matrix, field_name)
% Compare two matrices and return comparison statistics
    comparison = struct();
    comparison.field_name = field_name;
    comparison.matlab_size = size(matlab_matrix);
    comparison.cpp_size = size(cpp_matrix);
    comparison.dimensions_match = isequal(size(matlab_matrix), size(cpp_matrix));
    
    if comparison.dimensions_match
        % Calculate differences
        diff_matrix = matlab_matrix - cpp_matrix;
        comparison.max_abs_diff = max(abs(diff_matrix(:)));
        comparison.mean_abs_diff = mean(abs(diff_matrix(:)));
        comparison.rms_diff = sqrt(mean(diff_matrix(:).^2));
        
        % Calculate relative differences
        matlab_max = max(abs(matlab_matrix(:)));
        if matlab_max > 0
            comparison.max_rel_diff = comparison.max_abs_diff / matlab_max * 100;
            comparison.mean_rel_diff = comparison.mean_abs_diff / matlab_max * 100;
        else
            comparison.max_rel_diff = 0;
            comparison.mean_rel_diff = 0;
        end
        
        % Value ranges
        comparison.matlab_range = [min(matlab_matrix(:)), max(matlab_matrix(:))];
        comparison.cpp_range = [min(cpp_matrix(:)), max(cpp_matrix(:))];
        
        % Classification
        if comparison.max_rel_diff < 1e-10
            comparison.status = 'identical';
        elseif comparison.max_rel_diff < 1e-6
            comparison.status = 'excellent';
        elseif comparison.max_rel_diff < 1e-3
            comparison.status = 'good';
        elseif comparison.max_rel_diff < 1.0
            comparison.status = 'acceptable';
        else
            comparison.status = 'poor';
        end
    else
        comparison.status = 'dimension_mismatch';
    end
end

function generate_comparison_report(comparison_results, output_dir)
% Generate a comprehensive comparison report
    report_file = fullfile(output_dir, 'comparison_report.txt');
    fid = fopen(report_file, 'w');
    
    fprintf(fid, '=== MATLAB vs C++ 数据对比报告 ===\n');
    fprintf(fid, '生成时间: %s\n\n', datestr(now));
    
    domain_names = fieldnames(comparison_results);
    
    % Summary statistics
    total_domains = length(domain_names);
    total_matrices = 0;
    status_counts = struct('identical', 0, 'excellent', 0, 'good', 0, 'acceptable', 0, 'poor', 0, 'dimension_mismatch', 0, 'error', 0);
    
    fprintf(fid, '=== 总体统计 ===\n');
    fprintf(fid, '对比域数量: %d\n\n', total_domains);
    
    % Detailed comparison for each domain
    for i = 1:length(domain_names)
        domain_name = domain_names{i};
        domain_data = comparison_results.(domain_name);
        
        fprintf(fid, '=== %s ===\n', domain_name);
        
        if isfield(domain_data, 'error')
            fprintf(fid, '错误: %s\n\n', domain_data.error);
            status_counts.error = status_counts.error + 1;
            continue;
        end
        
        % Parameters comparison
        if isfield(domain_data, 'parameters')
            fprintf(fid, '参数对比:\n');
            param_fields = fieldnames(domain_data.parameters);
            for j = 1:length(param_fields)
                param = param_fields{j};
                param_data = domain_data.parameters.(param);
                if isfield(param_data, 'match')
                    fprintf(fid, '  %s: MATLAB=%.6g, C++=%.6g %s\n', param, param_data.matlab, param_data.cpp, iif(param_data.match, '✅', '❌'));
                elseif isfield(param_data, 'rel_diff')
                    fprintf(fid, '  %s: MATLAB=%.6e, C++=%.6e, 相对差异=%.2f%%\n', param, param_data.matlab, param_data.cpp, param_data.rel_diff);
                end
            end
            fprintf(fid, '\n');
        end
        
        % Matrices comparison
        if isfield(domain_data, 'matrices')
            fprintf(fid, '矩阵对比:\n');
            matrix_fields = fieldnames(domain_data.matrices);
            for j = 1:length(matrix_fields)
                field = matrix_fields{j};
                matrix_data = domain_data.matrices.(field);
                total_matrices = total_matrices + 1;
                
                if isfield(matrix_data, 'error')
                    fprintf(fid, '  %s: 错误 - %s\n', field, matrix_data.error);
                    status_counts.error = status_counts.error + 1;
                elseif isfield(matrix_data, 'status')
                    status = matrix_data.status;
                    status_counts.(status) = status_counts.(status) + 1;
                    
                    if strcmp(status, 'dimension_mismatch')
                        fprintf(fid, '  %s: 维度不匹配 - MATLAB: %s, C++: %s\n', field, mat2str(matrix_data.matlab_size), mat2str(matrix_data.cpp_size));
                    else
                        fprintf(fid, '  %s: %s - 最大相对差异: %.2e%%, 范围: MATLAB[%.2e,%.2e], C++[%.2e,%.2e]\n', ...
                                field, get_status_symbol(status), matrix_data.max_rel_diff, ...
                                matrix_data.matlab_range(1), matrix_data.matlab_range(2), ...
                                matrix_data.cpp_range(1), matrix_data.cpp_range(2));
                    end
                end
            end
        end
        fprintf(fid, '\n');
    end
    
    % Final summary
    fprintf(fid, '=== 最终统计 ===\n');
    fprintf(fid, '总矩阵数量: %d\n', total_matrices);
    fprintf(fid, '完全相同: %d (%.1f%%)\n', status_counts.identical, status_counts.identical/total_matrices*100);
    fprintf(fid, '优秀匹配: %d (%.1f%%)\n', status_counts.excellent, status_counts.excellent/total_matrices*100);
    fprintf(fid, '良好匹配: %d (%.1f%%)\n', status_counts.good, status_counts.good/total_matrices*100);
    fprintf(fid, '可接受匹配: %d (%.1f%%)\n', status_counts.acceptable, status_counts.acceptable/total_matrices*100);
    fprintf(fid, '匹配较差: %d (%.1f%%)\n', status_counts.poor, status_counts.poor/total_matrices*100);
    fprintf(fid, '维度不匹配: %d (%.1f%%)\n', status_counts.dimension_mismatch, status_counts.dimension_mismatch/total_matrices*100);
    fprintf(fid, '错误: %d (%.1f%%)\n', status_counts.error, status_counts.error/total_matrices*100);
    
    fclose(fid);
    
    % Also display summary in command window
    fprintf('\n=== 对比结果摘要 ===\n');
    fprintf('总域数量: %d, 总矩阵数量: %d\n', total_domains, total_matrices);
    fprintf('完全相同: %d, 优秀: %d, 良好: %d, 可接受: %d, 较差: %d\n', ...
            status_counts.identical, status_counts.excellent, status_counts.good, ...
            status_counts.acceptable, status_counts.poor);
    fprintf('维度不匹配: %d, 错误: %d\n', status_counts.dimension_mismatch, status_counts.error);
    
    % Save results to MAT file
    results_file = fullfile(output_dir, 'comparison_results.mat');
    save(results_file, 'comparison_results', 'status_counts');
    fprintf('详细报告: %s\n', report_file);
    fprintf('结果数据: %s\n', results_file);
end

function symbol = get_status_symbol(status)
% Get symbol for status
    switch status
        case 'identical'
            symbol = '🟢 完全相同';
        case 'excellent'
            symbol = '🟢 优秀';
        case 'good'
            symbol = '🟡 良好';
        case 'acceptable'
            symbol = '🟠 可接受';
        case 'poor'
            symbol = '🔴 较差';
        otherwise
            symbol = '❓ 未知';
    end
end

function result = iif(condition, true_val, false_val)
% Inline if function
    if condition
        result = true_val;
    else
        result = false_val;
    end
end
