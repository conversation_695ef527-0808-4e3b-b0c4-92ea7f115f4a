# MATLAB 域结构体调试信息
# 生成时间: 01-Aug-2025 15:48:01

## 域结构体字段列表 (共 100 个字段):
model2dA: struct
state: struct
Bx2: 空矩阵
Bx1: 空矩阵
Bz1: 空矩阵
Bz2: 空矩阵
px1: double, 大小: [1 1], 值: 5
pz1: double, 大小: [1 1], 值: 5
Nx1: double, 大小: [1 1], 值: 37
Nz1: double, 大小: [1 1], 值: 28
x2d11: double, 大小: [37 28], 值范围: [-8.637309e+05, -4.849601e-11]
z2d11: double, 大小: [37 28], 值范围: [-1.221500e+06, -4.242641e+05]
bxT1: double, 大小: [37 37], 值范围: [0.000000e+00, 1.000000e+00]
bxT2: double, 大小: [37 36], 值范围: [0.000000e+00, 1.000000e+00]
bzT1: double, 大小: [28 28], 值范围: [0.000000e+00, 1.000000e+00]
bzT2: double, 大小: [28 27], 值范围: [0.000000e+00, 1.000000e+00]
kkx12: double, 大小: [37 36], 值范围: [-5.555556e-01, 5.555556e-01]
kkz12: double, 大小: [28 27], 值范围: [-5.555556e-01, 5.555556e-01]
kkx21: double, 大小: [36 37], 值范围: [-4.444444e-01, 4.444444e-01]
kkz21: double, 大小: [27 28], 值范围: [-4.444444e-01, 4.444444e-01]
invLx11: double, 大小: [37 37], 值范围: [-1.793110e+01, 3.087304e+01]
invLz11: double, 大小: [28 28], 值范围: [-1.520183e+01, 2.617389e+01]
invLx22: double, 大小: [36 36], 值范围: [-1.104137e+01, 2.268873e+01]
invLz22: double, 大小: [27 27], 值范围: [-9.360779e+00, 1.923530e+01]
invLxT11: double, 大小: [37 37], 值范围: [-1.793110e+01, 3.087304e+01]
invLzT11: double, 大小: [28 28], 值范围: [-1.520183e+01, 2.617389e+01]
invLxT22: double, 大小: [36 36], 值范围: [-1.104137e+01, 2.268873e+01]
invLzT22: double, 大小: [27 27], 值范围: [-9.360779e+00, 1.923530e+01]
alpha_mo: double, 大小: [1 1], 值: 0.5
alpha_po: double, 大小: [1 1], 值: 0.5
alpha_om: double, 大小: [1 1], 值: 0
alpha_op: double, 大小: [1 1], 值: 0.5
iNbr_mo: double, 大小: [1 1], 值: 3
iNbr_po: double, 大小: [1 1], 值: 2
iNbr_om: double, 大小: [1 1], 值: 0
iNbr_op: double, 大小: [1 1], 值: 4
iFace_mo: double, 大小: [1 1], 值: 3
iFace_po: double, 大小: [1 1], 值: 1
iFace_om: double, 大小: [1 1], 值: 0
iFace_op: double, 大小: [1 1], 值: 3
rot_mo: double, 大小: [2 2], 值: [0 -1;-1 0]
rot_po: double, 大小: [2 2], 值: [1 0;0 1]
rot_om: double, 大小: [2 2], 值: [0 0;0 0]
rot_op: double, 大小: [2 2], 值: [1 0;0 1]
dxpdx11: double, 大小: [37 28], 值范围: [7.405095e-07, 2.239829e-06]
dzpdx11: double, 大小: [37 28], 值范围: [1.021858e-09, 1.133560e-06]
dxpdz11: double, 大小: [37 28], 值范围: [-1.871485e-06, -1.553012e-09]
dzpdz11: double, 大小: [37 28], 值范围: [1.139145e-06, 1.644556e-06]
dxpdx12: double, 大小: [37 27], 值范围: [7.408172e-07, 2.238400e-06]
dzpdx12: double, 大小: [37 27], 值范围: [1.022434e-09, 1.133325e-06]
dxpdz12: double, 大小: [37 27], 值范围: [-1.870074e-06, -1.553630e-09]
dzpdz12: double, 大小: [37 27], 值范围: [1.139380e-06, 1.644179e-06]
dxpdx21: double, 大小: [36 28], 值范围: [7.408527e-07, 2.239831e-06]
dzpdx21: double, 大小: [36 28], 值范围: [1.217533e-09, 1.133006e-06]
dxpdz21: double, 大小: [36 28], 值范围: [-1.870591e-06, -1.844578e-09]
dzpdz21: double, 大小: [36 28], 值范围: [1.139176e-06, 1.644486e-06]
dxpdx22: double, 大小: [36 27], 值范围: [7.411604e-07, 2.238402e-06]
dzpdx22: double, 大小: [36 27], 值范围: [1.218219e-09, 1.132771e-06]
dxpdz22: double, 大小: [36 27], 值范围: [-1.869181e-06, -1.845312e-09]
dzpdz22: double, 大小: [36 27], 值范围: [1.139412e-06, 1.644109e-06]
Jac11: double, 大小: [37 28], 值范围: [2.344023e+11, 7.865993e+11]
Jac22: double, 大小: [36 27], 值范围: [2.346122e+11, 7.862462e+11]
Jac12: double, 大小: [37 27], 值范围: [2.345792e+11, 7.862500e+11]
Jac21: double, 大小: [36 28], 值范围: [2.344353e+11, 7.865955e+11]
rho12: double, 大小: [1 1], 值: 2000
rho21: double, 大小: [1 1], 值: 2000
mu11: double, 大小: [1 1], 值: 15125000000
mu22: double, 大小: [1 1], 值: 15125000000
Dzx210mo: double, 大小: [27 28], 值范围: [0.000000e+00, 1.569993e-02]
Dzx120mo: double, 大小: [28 27], 值范围: [0.000000e+00, 1.569993e-02]
Dzx110mo: double, 大小: [28 28], 值范围: [0.000000e+00, 1.712720e-02]
Dzx220mo: double, 大小: [27 27], 值范围: [0.000000e+00, 1.871382e-02]
Dzz210po: double, 大小: [27 28], 值范围: [0.000000e+00, 1.569993e-02]
Dzz120po: double, 大小: [28 27], 值范围: [0.000000e+00, 1.569993e-02]
Dzz110po: double, 大小: [28 28], 值范围: [0.000000e+00, 1.712720e-02]
Dzz220po: double, 大小: [27 27], 值范围: [0.000000e+00, 1.871382e-02]
Dxx210op: double, 大小: [36 20], 值范围: [0.000000e+00, 1.589864e-02]
Dxx120op: double, 大小: [37 19], 值范围: [0.000000e+00, 1.682967e-02]
Dxx110op: double, 大小: [37 20], 值范围: [0.000000e+00, 1.566483e-02]
Dxx220op: double, 大小: [36 19], 值范围: [0.000000e+00, 1.710416e-02]
Dzz210mo: 空矩阵
Dzz120mo: 空矩阵
Dzz110mo: 空矩阵
Dzz220mo: 空矩阵
Dzx210po: 空矩阵
Dzx120po: 空矩阵
Dzx110po: 空矩阵
Dzx220po: 空矩阵
Dxz210om: 空矩阵
Dxz120om: 空矩阵
Dxz110om: 空矩阵
Dxz220om: 空矩阵
Dxx210om: 空矩阵
Dxx120om: 空矩阵
Dxx110om: 空矩阵
Dxx220om: 空矩阵
Dxz210op: 空矩阵
Dxz120op: 空矩阵
Dxz110op: 空矩阵
Dxz220op: 空矩阵

## 关键字段 Nx2: 不存在

## 关键字段 Nz2: 不存在

## 关键字段 x2d22: 不存在

## 关键字段 z2d22: 不存在

## 关键字段 region: 不存在
