
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineSystem.cmake:212 (message)"
      - "CMakeLists.txt:5 (project)"
    message: |
      The system is: Windows - 10.0.22631 - AMD64
  -
    kind: "find-v1"
    backtrace:
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:5 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCCompilerId.c.in"
    candidate_directories:
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/share/cmake-4.1/Modules/"
    found: "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/share/cmake-4.1/Modules/CMakeCCompilerId.c.in"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX86\\x86"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\VC\\VCPackages"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\TestWindow"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\TeamFoundation\\Team Explorer"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\bin\\Roslyn"
        - "C:\\Program Files (x86)\\Microsoft SDKs\\Windows\\v10.0A\\bin\\NETFX 4.8 Tools\\"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Team Tools\\DiagnosticsHub\\Collector"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\bin\\10.0.26100.0\\\\x86"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\bin\\\\x86"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\\\MSBuild\\Current\\Bin\\amd64"
        - "C:\\Windows\\Microsoft.NET\\Framework\\v4.0.30319"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\Tools\\"
        - "D:\\mpi\\Bin\\"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\ProgramData\\Oracle\\Java\\javapath"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\windows\\system32"
        - "C:\\windows"
        - "C:\\windows\\System32\\Wbem"
        - "C:\\windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\windows\\System32\\OpenSSH\\"
        - "C:\\sqlite"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Program Files (x86)\\pcsuite\\"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "D:\\CAMKE\\bin"
        - "D:\\mingw\\mingw64\\bin"
        - "C:\\Users\\<USER>\\Downloads\\x86_64-15.1.0-release-win32-seh-ucrt-rt_v12-rev0\\mingw64\\bin"
        - "D:\\matlab\\runtime\\win64"
        - "D:\\matlab\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\Downloads\\clion\\CLion 2025.1.2\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "E:\\Git\\cmd"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft V"
        - "C:\\Program Files\\nodejs\\"
        - "D:\\OpenBLAS-0.3.30-x64"
        - "D:\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\Downloads\\clion\\CLion 2025.1.2\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\Downloads\\cmake-4.1.0-rc4-windows-x86_64\\cmake-4.1.0-rc4-windows-x86_64\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Trae\\resources\\app\\bin\\lib"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\bin"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\Ninja"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\VC\\Linux\\bin\\ConnectionManagerExe"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\vcpkg"
      ENV{INCLUDE}:
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\\\include\\10.0.26100.0\\\\um"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\\\include\\10.0.26100.0\\\\shared"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\\\include\\10.0.26100.0\\\\winrt"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\\\include\\10.0.26100.0\\\\cppwinrt"
        - "C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um"
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:5 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      适用于 .NET Framework MSBuild 版本 17.14.10+8b8e13593
      生成启动时间为 2025/8/2 15:06:48。
      
      节点 1 上的项目“D:\\project\\build\\CMakeFiles\\4.1.0-rc4\\CompilerIdC\\CompilerIdC.vcxproj”(默认目标)。
      PrepareForBuild:
        正在创建目录“Debug\\”。
        已启用结构化输出。编译器诊断的格式设置将反映错误层次结构。有关详细信息，请参阅 https://aka.ms/cpp/structured-output。
        正在创建目录“Debug\\CompilerIdC.tlog\\”。
      InitializeBuildStatus:
        正在创建“Debug\\CompilerIdC.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
        正在对“Debug\\CompilerIdC.tlog\\unsuccessfulbuild”执行 Touch 任务。
      VcpkgTripletSelection:
        Using triplet "x86-windows" from "C:\\Users\\<USER>\\vcpkg\\installed\\x86-windows\\"
        Using normalized configuration "Release"
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX86\\x86\\CL.exe /c /I"C:\\Users\\<USER>\\vcpkg\\installed\\x86-windows\\include" /nologo /W0 /WX- /diagnostics:column /Od /Oy- /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TC /analyze- /FC /errorReport:queue CMakeCCompilerId.c
        CMakeCCompilerId.c
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX86\\x86\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdC.exe" /INCREMENTAL:NO /NOLOGO /LIBPATH:"C:\\Users\\<USER>\\vcpkg\\installed\\x86-windows\\lib" /LIBPATH:"C:\\Users\\<USER>\\vcpkg\\installed\\x86-windows\\lib\\manual-link" kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib "C:\\Users\\<USER>\\vcpkg\\installed\\x86-windows\\lib\\*.lib" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdC.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdC.lib" /MACHINE:X86 /SAFESEH Debug\\CMakeCCompilerId.obj
        CompilerIdC.vcxproj -> D:\\project\\build\\CMakeFiles\\4.1.0-rc4\\CompilerIdC\\CompilerIdC.exe
      AppLocalFromInstalled:
        pwsh.exe -ExecutionPolicy Bypass -noprofile -File "C:\\Users\\<USER>\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "D:\\project\\build\\CMakeFiles\\4.1.0-rc4\\CompilerIdC\\CompilerIdC.exe" "C:\\Users\\<USER>\\vcpkg\\installed\\x86-windows\\bin" "Debug\\CompilerIdC.tlog\\CompilerIdC.write.1u.tlog" "Debug\\vcpkg.applocal.log"
        'pwsh.exe' 不是内部或外部命令，也不是可运行的程序
        或批处理文件。
        命令“pwsh.exe -ExecutionPolicy Bypass -noprofile -File "C:\\Users\\<USER>\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "D:\\project\\build\\CMakeFiles\\4.1.0-rc4\\CompilerIdC\\CompilerIdC.exe" "C:\\Users\\<USER>\\vcpkg\\installed\\x86-windows\\bin" "Debug\\CompilerIdC.tlog\\CompilerIdC.write.1u.tlog" "Debug\\vcpkg.applocal.log"”已退出，代码为 9009。
        "C:\\windows\\System32\\WindowsPowerShell\\v1.0\\powershell.exe" -ExecutionPolicy Bypass -noprofile -File "C:\\Users\\<USER>\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "D:\\project\\build\\CMakeFiles\\4.1.0-rc4\\CompilerIdC\\CompilerIdC.exe" "C:\\Users\\<USER>\\vcpkg\\installed\\x86-windows\\bin" "Debug\\CompilerIdC.tlog\\CompilerIdC.write.1u.tlog" "Debug\\vcpkg.applocal.log"
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_C_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_C_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx86\\x86\\cl.exe
      FinalizeBuildStatus:
        正在删除文件“Debug\\CompilerIdC.tlog\\unsuccessfulbuild”。
        正在对“Debug\\CompilerIdC.tlog\\CompilerIdC.lastbuildstate”执行 Touch 任务。
      已完成生成项目“D:\\project\\build\\CMakeFiles\\4.1.0-rc4\\CompilerIdC\\CompilerIdC.vcxproj”(默认目标)的操作。
      
      已成功生成。
          0 个警告
          0 个错误
      
      已用时间 00:00:01.56
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.exe"
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.vcxproj"
      
      The C compiler identification is MSVC, found in:
        D:/project/build/CMakeFiles/4.1.0-rc4/CompilerIdC/CompilerIdC.exe
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:37 (find_program)"
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:65 (__resolve_tool_path)"
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:103 (__resolve_linker_path)"
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:5 (project)"
    mode: "program"
    variable: "_CMAKE_TOOL_WITH_PATH"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "link"
    candidate_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx86/x86/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/vcpackages/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TestWindow/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TeamFoundation/Team Explorer/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/Roslyn/"
      - "C:/Program Files (x86)/Microsoft SDKs/Windows/v10.0A/bin/NETFX 4.8 Tools/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Team Tools/DiagnosticsHub/Collector/"
      - "C:/Program Files (x86)/Windows Kits/10/bin/10.0.26100.0/x86/"
      - "C:/Program Files (x86)/Windows Kits/10/bin/x86/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/"
      - "C:/Windows/Microsoft.NET/Framework/v4.0.30319/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/Tools/"
      - "D:/mpi/Bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/ProgramData/Oracle/Java/javapath/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/sqlite/"
      - "C:/Program Files/dotnet/"
      - "C:/Program Files (x86)/pcsuite/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "D:/CAMKE/bin/"
      - "D:/mingw/mingw64/bin/"
      - "C:/Users/<USER>/Downloads/x86_64-15.1.0-release-win32-seh-ucrt-rt_v12-rev0/mingw64/bin/"
      - "D:/matlab/runtime/win64/"
      - "D:/matlab/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/.dotnet/tools/"
      - "C:/Users/<USER>/Downloads/clion/CLion 2025.1.2/bin/"
      - "E:/Git/cmd/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft V/"
      - "C:/Program Files/nodejs/"
      - "D:/OpenBLAS-0.3.30-x64/"
      - "D:/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Trae/resources/app/bin/lib/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/Linux/bin/ConnectionManagerExe/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/vcpkg/"
    searched_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx86/x86/link.com"
    found: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx86/x86/link.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX86\\x86"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\VC\\VCPackages"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\TestWindow"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\TeamFoundation\\Team Explorer"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\bin\\Roslyn"
        - "C:\\Program Files (x86)\\Microsoft SDKs\\Windows\\v10.0A\\bin\\NETFX 4.8 Tools\\"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Team Tools\\DiagnosticsHub\\Collector"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\bin\\10.0.26100.0\\\\x86"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\bin\\\\x86"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\\\MSBuild\\Current\\Bin\\amd64"
        - "C:\\Windows\\Microsoft.NET\\Framework\\v4.0.30319"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\Tools\\"
        - "D:\\mpi\\Bin\\"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\ProgramData\\Oracle\\Java\\javapath"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\windows\\system32"
        - "C:\\windows"
        - "C:\\windows\\System32\\Wbem"
        - "C:\\windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\windows\\System32\\OpenSSH\\"
        - "C:\\sqlite"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Program Files (x86)\\pcsuite\\"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "D:\\CAMKE\\bin"
        - "D:\\mingw\\mingw64\\bin"
        - "C:\\Users\\<USER>\\Downloads\\x86_64-15.1.0-release-win32-seh-ucrt-rt_v12-rev0\\mingw64\\bin"
        - "D:\\matlab\\runtime\\win64"
        - "D:\\matlab\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\Downloads\\clion\\CLion 2025.1.2\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "E:\\Git\\cmd"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft V"
        - "C:\\Program Files\\nodejs\\"
        - "D:\\OpenBLAS-0.3.30-x64"
        - "D:\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\Downloads\\clion\\CLion 2025.1.2\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\Downloads\\cmake-4.1.0-rc4-windows-x86_64\\cmake-4.1.0-rc4-windows-x86_64\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Trae\\resources\\app\\bin\\lib"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\bin"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\Ninja"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\VC\\Linux\\bin\\ConnectionManagerExe"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\vcpkg"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:37 (find_program)"
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:65 (__resolve_tool_path)"
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:104 (__resolve_linker_path)"
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:5 (project)"
    mode: "program"
    variable: "_CMAKE_TOOL_WITH_PATH"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "lld-link"
    candidate_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx86/x86/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/vcpackages/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TestWindow/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TeamFoundation/Team Explorer/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/Roslyn/"
      - "C:/Program Files (x86)/Microsoft SDKs/Windows/v10.0A/bin/NETFX 4.8 Tools/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Team Tools/DiagnosticsHub/Collector/"
      - "C:/Program Files (x86)/Windows Kits/10/bin/10.0.26100.0/x86/"
      - "C:/Program Files (x86)/Windows Kits/10/bin/x86/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/"
      - "C:/Windows/Microsoft.NET/Framework/v4.0.30319/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/Tools/"
      - "D:/mpi/Bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/ProgramData/Oracle/Java/javapath/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/sqlite/"
      - "C:/Program Files/dotnet/"
      - "C:/Program Files (x86)/pcsuite/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "D:/CAMKE/bin/"
      - "D:/mingw/mingw64/bin/"
      - "C:/Users/<USER>/Downloads/x86_64-15.1.0-release-win32-seh-ucrt-rt_v12-rev0/mingw64/bin/"
      - "D:/matlab/runtime/win64/"
      - "D:/matlab/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/.dotnet/tools/"
      - "C:/Users/<USER>/Downloads/clion/CLion 2025.1.2/bin/"
      - "E:/Git/cmd/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft V/"
      - "C:/Program Files/nodejs/"
      - "D:/OpenBLAS-0.3.30-x64/"
      - "D:/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Trae/resources/app/bin/lib/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/Linux/bin/ConnectionManagerExe/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/vcpkg/"
    searched_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx86/x86/lld-link.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx86/x86/lld-link.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx86/x86/lld-link"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/vcpackages/lld-link.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/vcpackages/lld-link.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/vcpackages/lld-link"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TestWindow/lld-link.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TestWindow/lld-link.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TestWindow/lld-link"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TeamFoundation/Team Explorer/lld-link.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TeamFoundation/Team Explorer/lld-link.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TeamFoundation/Team Explorer/lld-link"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/Roslyn/lld-link.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/Roslyn/lld-link.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/Roslyn/lld-link"
      - "C:/Program Files (x86)/Microsoft SDKs/Windows/v10.0A/bin/NETFX 4.8 Tools/lld-link.com"
      - "C:/Program Files (x86)/Microsoft SDKs/Windows/v10.0A/bin/NETFX 4.8 Tools/lld-link.exe"
      - "C:/Program Files (x86)/Microsoft SDKs/Windows/v10.0A/bin/NETFX 4.8 Tools/lld-link"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Team Tools/DiagnosticsHub/Collector/lld-link.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Team Tools/DiagnosticsHub/Collector/lld-link.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Team Tools/DiagnosticsHub/Collector/lld-link"
      - "C:/Program Files (x86)/Windows Kits/10/bin/10.0.26100.0/x86/lld-link.com"
      - "C:/Program Files (x86)/Windows Kits/10/bin/10.0.26100.0/x86/lld-link.exe"
      - "C:/Program Files (x86)/Windows Kits/10/bin/10.0.26100.0/x86/lld-link"
      - "C:/Program Files (x86)/Windows Kits/10/bin/x86/lld-link.com"
      - "C:/Program Files (x86)/Windows Kits/10/bin/x86/lld-link.exe"
      - "C:/Program Files (x86)/Windows Kits/10/bin/x86/lld-link"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/lld-link.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/lld-link.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/lld-link"
      - "C:/Windows/Microsoft.NET/Framework/v4.0.30319/lld-link.com"
      - "C:/Windows/Microsoft.NET/Framework/v4.0.30319/lld-link.exe"
      - "C:/Windows/Microsoft.NET/Framework/v4.0.30319/lld-link"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/lld-link.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/lld-link.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/lld-link"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/Tools/lld-link.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/Tools/lld-link.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/Tools/lld-link"
      - "D:/mpi/Bin/lld-link.com"
      - "D:/mpi/Bin/lld-link.exe"
      - "D:/mpi/Bin/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/lld-link"
      - "C:/ProgramData/Oracle/Java/javapath/lld-link.com"
      - "C:/ProgramData/Oracle/Java/javapath/lld-link.exe"
      - "C:/ProgramData/Oracle/Java/javapath/lld-link"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/lld-link.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/lld-link.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/lld-link"
      - "C:/Windows/System32/lld-link.com"
      - "C:/Windows/System32/lld-link.exe"
      - "C:/Windows/System32/lld-link"
      - "C:/Windows/lld-link.com"
      - "C:/Windows/lld-link.exe"
      - "C:/Windows/lld-link"
      - "C:/Windows/System32/wbem/lld-link.com"
      - "C:/Windows/System32/wbem/lld-link.exe"
      - "C:/Windows/System32/wbem/lld-link"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link"
      - "C:/Windows/System32/OpenSSH/lld-link.com"
      - "C:/Windows/System32/OpenSSH/lld-link.exe"
      - "C:/Windows/System32/OpenSSH/lld-link"
      - "C:/sqlite/lld-link.com"
      - "C:/sqlite/lld-link.exe"
      - "C:/sqlite/lld-link"
      - "C:/Program Files/dotnet/lld-link.com"
      - "C:/Program Files/dotnet/lld-link.exe"
      - "C:/Program Files/dotnet/lld-link"
      - "C:/Program Files (x86)/pcsuite/lld-link.com"
      - "C:/Program Files (x86)/pcsuite/lld-link.exe"
      - "C:/Program Files (x86)/pcsuite/lld-link"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/lld-link.com"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/lld-link.exe"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/lld-link"
      - "D:/CAMKE/bin/lld-link.com"
      - "D:/CAMKE/bin/lld-link.exe"
      - "D:/CAMKE/bin/lld-link"
      - "D:/mingw/mingw64/bin/lld-link.com"
      - "D:/mingw/mingw64/bin/lld-link.exe"
      - "D:/mingw/mingw64/bin/lld-link"
      - "C:/Users/<USER>/Downloads/x86_64-15.1.0-release-win32-seh-ucrt-rt_v12-rev0/mingw64/bin/lld-link.com"
      - "C:/Users/<USER>/Downloads/x86_64-15.1.0-release-win32-seh-ucrt-rt_v12-rev0/mingw64/bin/lld-link.exe"
      - "C:/Users/<USER>/Downloads/x86_64-15.1.0-release-win32-seh-ucrt-rt_v12-rev0/mingw64/bin/lld-link"
      - "D:/matlab/runtime/win64/lld-link.com"
      - "D:/matlab/runtime/win64/lld-link.exe"
      - "D:/matlab/runtime/win64/lld-link"
      - "D:/matlab/bin/lld-link.com"
      - "D:/matlab/bin/lld-link.exe"
      - "D:/matlab/bin/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Scripts/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Scripts/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Scripts/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/lld-link"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link"
      - "C:/Users/<USER>/.dotnet/tools/lld-link.com"
      - "C:/Users/<USER>/.dotnet/tools/lld-link.exe"
      - "C:/Users/<USER>/.dotnet/tools/lld-link"
      - "C:/Users/<USER>/Downloads/clion/CLion 2025.1.2/bin/lld-link.com"
      - "C:/Users/<USER>/Downloads/clion/CLion 2025.1.2/bin/lld-link.exe"
      - "C:/Users/<USER>/Downloads/clion/CLion 2025.1.2/bin/lld-link"
      - "E:/Git/cmd/lld-link.com"
      - "E:/Git/cmd/lld-link.exe"
      - "E:/Git/cmd/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft V/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft V/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft V/lld-link"
      - "C:/Program Files/nodejs/lld-link.com"
      - "C:/Program Files/nodejs/lld-link.exe"
      - "C:/Program Files/nodejs/lld-link"
      - "D:/OpenBLAS-0.3.30-x64/lld-link.com"
      - "D:/OpenBLAS-0.3.30-x64/lld-link.exe"
      - "D:/OpenBLAS-0.3.30-x64/lld-link"
      - "D:/Microsoft VS Code/bin/lld-link.com"
      - "D:/Microsoft VS Code/bin/lld-link.exe"
      - "D:/Microsoft VS Code/bin/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/lld-link"
      - "C:/Users/<USER>/AppData/Roaming/npm/lld-link.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/lld-link.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/lld-link"
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/bin/lld-link.com"
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/bin/lld-link.exe"
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/bin/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Trae/resources/app/bin/lib/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Trae/resources/app/bin/lib/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Trae/resources/app/bin/lib/lld-link"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/lld-link.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/lld-link.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/lld-link"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/lld-link.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/lld-link.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/lld-link"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/Linux/bin/ConnectionManagerExe/lld-link.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/Linux/bin/ConnectionManagerExe/lld-link.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/Linux/bin/ConnectionManagerExe/lld-link"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/vcpkg/lld-link.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/vcpkg/lld-link.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/vcpkg/lld-link"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX86\\x86"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\VC\\VCPackages"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\TestWindow"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\TeamFoundation\\Team Explorer"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\bin\\Roslyn"
        - "C:\\Program Files (x86)\\Microsoft SDKs\\Windows\\v10.0A\\bin\\NETFX 4.8 Tools\\"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Team Tools\\DiagnosticsHub\\Collector"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\bin\\10.0.26100.0\\\\x86"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\bin\\\\x86"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\\\MSBuild\\Current\\Bin\\amd64"
        - "C:\\Windows\\Microsoft.NET\\Framework\\v4.0.30319"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\Tools\\"
        - "D:\\mpi\\Bin\\"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\ProgramData\\Oracle\\Java\\javapath"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\windows\\system32"
        - "C:\\windows"
        - "C:\\windows\\System32\\Wbem"
        - "C:\\windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\windows\\System32\\OpenSSH\\"
        - "C:\\sqlite"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Program Files (x86)\\pcsuite\\"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "D:\\CAMKE\\bin"
        - "D:\\mingw\\mingw64\\bin"
        - "C:\\Users\\<USER>\\Downloads\\x86_64-15.1.0-release-win32-seh-ucrt-rt_v12-rev0\\mingw64\\bin"
        - "D:\\matlab\\runtime\\win64"
        - "D:\\matlab\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\Downloads\\clion\\CLion 2025.1.2\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "E:\\Git\\cmd"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft V"
        - "C:\\Program Files\\nodejs\\"
        - "D:\\OpenBLAS-0.3.30-x64"
        - "D:\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\Downloads\\clion\\CLion 2025.1.2\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\Downloads\\cmake-4.1.0-rc4-windows-x86_64\\cmake-4.1.0-rc4-windows-x86_64\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Trae\\resources\\app\\bin\\lib"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\bin"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\Ninja"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\VC\\Linux\\bin\\ConnectionManagerExe"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\vcpkg"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:5 (project)"
    mode: "program"
    variable: "CMAKE_LINKER"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "link"
    candidate_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx86/x86/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/vcpackages/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TestWindow/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TeamFoundation/Team Explorer/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/Roslyn/"
      - "C:/Program Files (x86)/Microsoft SDKs/Windows/v10.0A/bin/NETFX 4.8 Tools/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Team Tools/DiagnosticsHub/Collector/"
      - "C:/Program Files (x86)/Windows Kits/10/bin/10.0.26100.0/x86/"
      - "C:/Program Files (x86)/Windows Kits/10/bin/x86/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/"
      - "C:/Windows/Microsoft.NET/Framework/v4.0.30319/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/Tools/"
      - "D:/mpi/Bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/ProgramData/Oracle/Java/javapath/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/sqlite/"
      - "C:/Program Files/dotnet/"
      - "C:/Program Files (x86)/pcsuite/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "D:/CAMKE/bin/"
      - "D:/mingw/mingw64/bin/"
      - "C:/Users/<USER>/Downloads/x86_64-15.1.0-release-win32-seh-ucrt-rt_v12-rev0/mingw64/bin/"
      - "D:/matlab/runtime/win64/"
      - "D:/matlab/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/.dotnet/tools/"
      - "C:/Users/<USER>/Downloads/clion/CLion 2025.1.2/bin/"
      - "E:/Git/cmd/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft V/"
      - "C:/Program Files/nodejs/"
      - "D:/OpenBLAS-0.3.30-x64/"
      - "D:/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Trae/resources/app/bin/lib/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/Linux/bin/ConnectionManagerExe/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/vcpkg/"
    searched_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx86/x86/link.com"
    found: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx86/x86/link.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX86\\x86"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\VC\\VCPackages"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\TestWindow"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\TeamFoundation\\Team Explorer"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\bin\\Roslyn"
        - "C:\\Program Files (x86)\\Microsoft SDKs\\Windows\\v10.0A\\bin\\NETFX 4.8 Tools\\"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Team Tools\\DiagnosticsHub\\Collector"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\bin\\10.0.26100.0\\\\x86"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\bin\\\\x86"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\\\MSBuild\\Current\\Bin\\amd64"
        - "C:\\Windows\\Microsoft.NET\\Framework\\v4.0.30319"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\Tools\\"
        - "D:\\mpi\\Bin\\"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\ProgramData\\Oracle\\Java\\javapath"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\windows\\system32"
        - "C:\\windows"
        - "C:\\windows\\System32\\Wbem"
        - "C:\\windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\windows\\System32\\OpenSSH\\"
        - "C:\\sqlite"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Program Files (x86)\\pcsuite\\"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "D:\\CAMKE\\bin"
        - "D:\\mingw\\mingw64\\bin"
        - "C:\\Users\\<USER>\\Downloads\\x86_64-15.1.0-release-win32-seh-ucrt-rt_v12-rev0\\mingw64\\bin"
        - "D:\\matlab\\runtime\\win64"
        - "D:\\matlab\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\Downloads\\clion\\CLion 2025.1.2\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "E:\\Git\\cmd"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft V"
        - "C:\\Program Files\\nodejs\\"
        - "D:\\OpenBLAS-0.3.30-x64"
        - "D:\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\Downloads\\clion\\CLion 2025.1.2\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\Downloads\\cmake-4.1.0-rc4-windows-x86_64\\cmake-4.1.0-rc4-windows-x86_64\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Trae\\resources\\app\\bin\\lib"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\bin"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\Ninja"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\VC\\Linux\\bin\\ConnectionManagerExe"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\vcpkg"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:5 (project)"
    mode: "program"
    variable: "CMAKE_MT"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "mt"
    candidate_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx86/x86/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/vcpackages/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TestWindow/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TeamFoundation/Team Explorer/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/Roslyn/"
      - "C:/Program Files (x86)/Microsoft SDKs/Windows/v10.0A/bin/NETFX 4.8 Tools/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Team Tools/DiagnosticsHub/Collector/"
      - "C:/Program Files (x86)/Windows Kits/10/bin/10.0.26100.0/x86/"
      - "C:/Program Files (x86)/Windows Kits/10/bin/x86/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/"
      - "C:/Windows/Microsoft.NET/Framework/v4.0.30319/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/Tools/"
      - "D:/mpi/Bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/ProgramData/Oracle/Java/javapath/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/sqlite/"
      - "C:/Program Files/dotnet/"
      - "C:/Program Files (x86)/pcsuite/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "D:/CAMKE/bin/"
      - "D:/mingw/mingw64/bin/"
      - "C:/Users/<USER>/Downloads/x86_64-15.1.0-release-win32-seh-ucrt-rt_v12-rev0/mingw64/bin/"
      - "D:/matlab/runtime/win64/"
      - "D:/matlab/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/.dotnet/tools/"
      - "C:/Users/<USER>/Downloads/clion/CLion 2025.1.2/bin/"
      - "E:/Git/cmd/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft V/"
      - "C:/Program Files/nodejs/"
      - "D:/OpenBLAS-0.3.30-x64/"
      - "D:/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Trae/resources/app/bin/lib/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/Linux/bin/ConnectionManagerExe/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/vcpkg/"
    searched_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx86/x86/mt.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx86/x86/mt.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx86/x86/mt"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/vcpackages/mt.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/vcpackages/mt.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/vcpackages/mt"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TestWindow/mt.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TestWindow/mt.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TestWindow/mt"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TeamFoundation/Team Explorer/mt.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TeamFoundation/Team Explorer/mt.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TeamFoundation/Team Explorer/mt"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/Roslyn/mt.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/Roslyn/mt.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/Roslyn/mt"
      - "C:/Program Files (x86)/Microsoft SDKs/Windows/v10.0A/bin/NETFX 4.8 Tools/mt.com"
      - "C:/Program Files (x86)/Microsoft SDKs/Windows/v10.0A/bin/NETFX 4.8 Tools/mt.exe"
      - "C:/Program Files (x86)/Microsoft SDKs/Windows/v10.0A/bin/NETFX 4.8 Tools/mt"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Team Tools/DiagnosticsHub/Collector/mt.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Team Tools/DiagnosticsHub/Collector/mt.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Team Tools/DiagnosticsHub/Collector/mt"
      - "C:/Program Files (x86)/Windows Kits/10/bin/10.0.26100.0/x86/mt.com"
    found: "C:/Program Files (x86)/Windows Kits/10/bin/10.0.26100.0/x86/mt.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX86\\x86"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\VC\\VCPackages"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\TestWindow"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\TeamFoundation\\Team Explorer"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\bin\\Roslyn"
        - "C:\\Program Files (x86)\\Microsoft SDKs\\Windows\\v10.0A\\bin\\NETFX 4.8 Tools\\"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Team Tools\\DiagnosticsHub\\Collector"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\bin\\10.0.26100.0\\\\x86"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\bin\\\\x86"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\\\MSBuild\\Current\\Bin\\amd64"
        - "C:\\Windows\\Microsoft.NET\\Framework\\v4.0.30319"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\Tools\\"
        - "D:\\mpi\\Bin\\"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\ProgramData\\Oracle\\Java\\javapath"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\windows\\system32"
        - "C:\\windows"
        - "C:\\windows\\System32\\Wbem"
        - "C:\\windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\windows\\System32\\OpenSSH\\"
        - "C:\\sqlite"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Program Files (x86)\\pcsuite\\"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "D:\\CAMKE\\bin"
        - "D:\\mingw\\mingw64\\bin"
        - "C:\\Users\\<USER>\\Downloads\\x86_64-15.1.0-release-win32-seh-ucrt-rt_v12-rev0\\mingw64\\bin"
        - "D:\\matlab\\runtime\\win64"
        - "D:\\matlab\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\Downloads\\clion\\CLion 2025.1.2\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "E:\\Git\\cmd"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft V"
        - "C:\\Program Files\\nodejs\\"
        - "D:\\OpenBLAS-0.3.30-x64"
        - "D:\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\Downloads\\clion\\CLion 2025.1.2\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\Downloads\\cmake-4.1.0-rc4-windows-x86_64\\cmake-4.1.0-rc4-windows-x86_64\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Trae\\resources\\app\\bin\\lib"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\bin"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\Ninja"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\VC\\Linux\\bin\\ConnectionManagerExe"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\vcpkg"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:5 (project)"
    mode: "program"
    variable: "CMAKE_AR"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "lib"
    candidate_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx86/x86/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/vcpackages/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TestWindow/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TeamFoundation/Team Explorer/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/Roslyn/"
      - "C:/Program Files (x86)/Microsoft SDKs/Windows/v10.0A/bin/NETFX 4.8 Tools/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Team Tools/DiagnosticsHub/Collector/"
      - "C:/Program Files (x86)/Windows Kits/10/bin/10.0.26100.0/x86/"
      - "C:/Program Files (x86)/Windows Kits/10/bin/x86/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/"
      - "C:/Windows/Microsoft.NET/Framework/v4.0.30319/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/Tools/"
      - "D:/mpi/Bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/ProgramData/Oracle/Java/javapath/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/sqlite/"
      - "C:/Program Files/dotnet/"
      - "C:/Program Files (x86)/pcsuite/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "D:/CAMKE/bin/"
      - "D:/mingw/mingw64/bin/"
      - "C:/Users/<USER>/Downloads/x86_64-15.1.0-release-win32-seh-ucrt-rt_v12-rev0/mingw64/bin/"
      - "D:/matlab/runtime/win64/"
      - "D:/matlab/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/.dotnet/tools/"
      - "C:/Users/<USER>/Downloads/clion/CLion 2025.1.2/bin/"
      - "E:/Git/cmd/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft V/"
      - "C:/Program Files/nodejs/"
      - "D:/OpenBLAS-0.3.30-x64/"
      - "D:/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Trae/resources/app/bin/lib/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/Linux/bin/ConnectionManagerExe/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/vcpkg/"
    searched_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx86/x86/lib.com"
    found: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx86/x86/lib.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX86\\x86"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\VC\\VCPackages"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\TestWindow"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\TeamFoundation\\Team Explorer"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\bin\\Roslyn"
        - "C:\\Program Files (x86)\\Microsoft SDKs\\Windows\\v10.0A\\bin\\NETFX 4.8 Tools\\"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Team Tools\\DiagnosticsHub\\Collector"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\bin\\10.0.26100.0\\\\x86"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\bin\\\\x86"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\\\MSBuild\\Current\\Bin\\amd64"
        - "C:\\Windows\\Microsoft.NET\\Framework\\v4.0.30319"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\Tools\\"
        - "D:\\mpi\\Bin\\"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\ProgramData\\Oracle\\Java\\javapath"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\windows\\system32"
        - "C:\\windows"
        - "C:\\windows\\System32\\Wbem"
        - "C:\\windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\windows\\System32\\OpenSSH\\"
        - "C:\\sqlite"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Program Files (x86)\\pcsuite\\"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "D:\\CAMKE\\bin"
        - "D:\\mingw\\mingw64\\bin"
        - "C:\\Users\\<USER>\\Downloads\\x86_64-15.1.0-release-win32-seh-ucrt-rt_v12-rev0\\mingw64\\bin"
        - "D:\\matlab\\runtime\\win64"
        - "D:\\matlab\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\Downloads\\clion\\CLion 2025.1.2\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "E:\\Git\\cmd"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft V"
        - "C:\\Program Files\\nodejs\\"
        - "D:\\OpenBLAS-0.3.30-x64"
        - "D:\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\Downloads\\clion\\CLion 2025.1.2\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\Downloads\\cmake-4.1.0-rc4-windows-x86_64\\cmake-4.1.0-rc4-windows-x86_64\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Trae\\resources\\app\\bin\\lib"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\bin"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\Ninja"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\VC\\Linux\\bin\\ConnectionManagerExe"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\vcpkg"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineFortranCompiler.cmake:190 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:5 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeFortranCompilerId.F.in"
    candidate_directories:
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/share/cmake-4.1/Modules/"
    found: "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/share/cmake-4.1/Modules/CMakeFortranCompilerId.F.in"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX86\\x86"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\VC\\VCPackages"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\TestWindow"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\TeamFoundation\\Team Explorer"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\bin\\Roslyn"
        - "C:\\Program Files (x86)\\Microsoft SDKs\\Windows\\v10.0A\\bin\\NETFX 4.8 Tools\\"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Team Tools\\DiagnosticsHub\\Collector"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\bin\\10.0.26100.0\\\\x86"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\bin\\\\x86"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\\\MSBuild\\Current\\Bin\\amd64"
        - "C:\\Windows\\Microsoft.NET\\Framework\\v4.0.30319"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\Tools\\"
        - "D:\\mpi\\Bin\\"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\ProgramData\\Oracle\\Java\\javapath"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\windows\\system32"
        - "C:\\windows"
        - "C:\\windows\\System32\\Wbem"
        - "C:\\windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\windows\\System32\\OpenSSH\\"
        - "C:\\sqlite"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Program Files (x86)\\pcsuite\\"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "D:\\CAMKE\\bin"
        - "D:\\mingw\\mingw64\\bin"
        - "C:\\Users\\<USER>\\Downloads\\x86_64-15.1.0-release-win32-seh-ucrt-rt_v12-rev0\\mingw64\\bin"
        - "D:\\matlab\\runtime\\win64"
        - "D:\\matlab\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\Downloads\\clion\\CLion 2025.1.2\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "E:\\Git\\cmd"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft V"
        - "C:\\Program Files\\nodejs\\"
        - "D:\\OpenBLAS-0.3.30-x64"
        - "D:\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\Downloads\\clion\\CLion 2025.1.2\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\Downloads\\cmake-4.1.0-rc4-windows-x86_64\\cmake-4.1.0-rc4-windows-x86_64\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Trae\\resources\\app\\bin\\lib"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\bin"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\Ninja"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\VC\\Linux\\bin\\ConnectionManagerExe"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\vcpkg"
      ENV{INCLUDE}:
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\\\include\\10.0.26100.0\\\\um"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\\\include\\10.0.26100.0\\\\shared"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\\\include\\10.0.26100.0\\\\winrt"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\\\include\\10.0.26100.0\\\\cppwinrt"
        - "C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um"
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineFortranCompiler.cmake:190 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:5 (project)"
    message: |
      Compiling the Fortran compiler identification source file "CMakeFortranCompilerId.F" failed.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      1
      
      Microsoft Visual Studio 2022 \xb0汾 17.14.6 (June 2025).
      \xb0\xe6Ȩ\xcb\xf9\xd3\xd0(C) Microsoft Corp.\xa1\xa3\xb1\xa3\xc1\xf4\xcb\xf9\xd3\xd0Ȩ\xc0\xfb\xa1\xa3
      
      \xbd\xe2\xbe\xf6\xb7\xbd\xb0\xb8\xc5\xe4\xd6\xc3\xce\xdeЧ
      
      \xd3÷\xa8: 
      devenv [\xbd\xe2\xbe\xf6\xb7\xbd\xb0\xb8\xceļ\xfe | \xcf\xeeĿ\xceļ\xfe | \xceļ\xfe\xbc\xd0 | \xc8\xce\xd2\xe2\xceļ\xfe.\xc0\xa9չ\xc3\xfb] [\xbf\xaa\xb9\xd8]
      
      devenv \xb5ĵ\xdaһ\xb8\xf6\xb2\xce\xca\xfdͨ\xb3\xa3\xca\xc7һ\xb8\xf6\xbd\xe2\xbe\xf6\xb7\xbd\xb0\xb8\xceļ\xfe\xa1\xa2\xcf\xeeĿ\xceļ\xfe\xbb\xf2\xceļ\xfe\xbcС\xa3
      \xc4㻹\xbf\xc9\xd2\xd4ʹ\xd3\xc3\xd4ڱ༭\xc6\xf7\xd6\xd0\xd7Զ\xaf\xb4\xf2\xbf\xaa\xceļ\xfe\xa3\xac
      Ҳ\xbf\xc9\xd2\xd4ʹ\xd3\xc3\xc8κ\xce\xc6\xe4\xcb\xfb\xceļ\xfe\xd7\xf7Ϊ\xb5\xdaһ\xb8\xf6\xb2\xce\xca\xfd\xa1\xa3\xb5\xb1\xc4\xfa\xca\xe4\xc8\xeb\xcf\xeeĿ\xceļ\xfeʱ\xa3\xacIDE
      \xbb\xe1\xd4\xda\xcf\xeeĿ\xceļ\xfe\xb5ĸ\xb8Ŀ¼\xd6в\xe9\xd5\xd2\xd3\xeb\xb8\xc3\xcf\xeeĿ\xceļ\xfe\xbe\xdf\xd3\xd0\xcf\xe0ͬ
      \xbb\xf9\xc3\xfb\xb3Ƶ\xc4 .sln \xceļ\xfe\xa1\xa3\xc8\xe7\xb9\xfb\xb2\xbb\xb4\xe6\xd4\xda\xd5\xe2\xd1\xf9\xb5\xc4 .sln \xceļ\xfe\xa3\xac
      IDE \xbd\xab\xb2\xe9\xd5\xd2\xd2\xfd\xd3ø\xc3\xcf\xeeĿ\xb5ĵ\xa5\xb8\xf6 .sln \xceļ\xfe\xa1\xa3\xc8\xe7\xb9\xfb\xb2\xbb\xb4\xe6\xd4\xda\xd5\xe2\xd1\xf9\xb5ĵ\xa5\xb8\xf6
      .sln \xceļ\xfe\xa3\xac\xd4\xf2 IDE \xbd\xab\xb4\xb4\xbd\xa8һ\xb8\xf6\xbe\xdf\xd3\xd0Ĭ\xc8\xcf .sln \xceļ\xfe\xc3\xfb\xb5\xc4δ\xb1\xa3\xb4\xe6
      \xb5Ľ\xe2\xbe\xf6\xb7\xbd\xb0\xb8\xa3\xac\xc6\xe4\xbb\xf9\xc3\xfb\xb3\xc6\xd3\xeb\xcf\xeeĿ\xceļ\xfe\xb5Ļ\xf9\xc3\xfb\xb3\xc6\xcf\xe0ͬ\xa1\xa3
      
      \xc3\xfc\xc1\xee\xd0\xd0\xc9\xfa\xb3\xc9: 
      devenv \xbd\xe2\xbe\xf6\xb7\xbd\xb0\xb8\xceļ\xfe.sln /build [ \xbd\xe2\xbe\xf6\xb7\xbd\xb0\xb8\xc5\xe4\xd6\xc3 ] [ /project \xcf\xeeĿ\xc3\xfb\xb3ƻ\xf2\xceļ\xfe [ /projectconfig \xc3\xfb\xb3\xc6 ] ]
      \xbf\xc9\xd3õ\xc4\xc3\xfc\xc1\xee\xd0п\xaa\xb9\xd8: 
      
      /Build			ʹ\xd3\xc3ָ\xb6\xa8\xb5Ľ\xe2\xbe\xf6\xb7\xbd\xb0\xb8\xc5\xe4\xd6\xc3\xc9\xfa\xb3ɽ\xe2\xbe\xf6\xb7\xbd\xb0\xb8\xbb\xf2
      			\xcf\xeeĿ\xa1\xa3\xc0\xfd\xc8\xe7 "Debug"\xa1\xa3\xc8\xe7\xb9\xfb\xbf\xc9\xc4ܴ\xe6\xd4ڶ\xe0\xb8\xf6
      			ƽ̨\xa3\xac\xd4\xf2\xc5\xe4\xd6\xc3\xc3\xfb\xb3Ʊ\xd8\xd0\xeb\xd3\xc3\xd2\xfd\xba\xc5\xc0\xa8\xc6\xf0\xc0\xb4
      			\xb2\xa2\xb0\xfc\xba\xacƽ̨\xc3\xfb\xb3ơ\xa3\xc0\xfd\xc8\xe7 "Debug|Win32"\xa1\xa3
      /Clean			ɾ\xb3\xfd\xc9\xfa\xb3ɽ\xe1\xb9\xfb\xa1\xa3
      /Command		\xc6\xf4\xb6\xaf IDE \xb2\xa2ִ\xd0и\xc3\xc3\xfc\xc1
      /Deploy			\xc9\xfa\xb3ɲ\xa2\xb2\xbf\xca\xf0ָ\xb6\xa8\xb5\xc4\xc9\xfa\xb3\xc9\xc5\xe4\xd6á\xa3
      /DoNotLoadProjects	\xb4\xf2\xbf\xaaָ\xb6\xa8\xb5Ľ\xe2\xbe\xf6\xb7\xbd\xb0\xb8\xc7Ҳ\xbb\xbc\xd3\xd4\xd8\xc8κ\xce\xcf\xeeĿ\xa1\xa3
      /Edit			\xd4ڴ\xcbӦ\xd3ó\xcc\xd0\xf2\xb5\xc4\xd4\xcb\xd0\xd0ʵ\xc0\xfd\xd6д\xf2\xbf\xaa
      			ָ\xb6\xa8\xb5\xc4\xceļ\xfe\xa1\xa3\xc8\xe7\xb9\xfbû\xd3\xd0\xd5\xfd\xd4\xda\xd4\xcb\xd0е\xc4ʵ\xc0\xfd\xa3\xac
      			\xd4\xf2\xc6\xf4\xb6\xafһ\xb8\xf6\xbe\xdf\xd3м򻯴\xb0\xbfڲ\xbc\xbeֵ\xc4\xd0\xc2ʵ\xc0\xfd\xa1\xa3
      /LCID			\xc9\xe8\xd6\xc3 IDE \xd6\xd0\xd3\xc3\xd3\xda\xd3û\xa7\xbd\xe7\xc3\xe6\xb5\xc4Ĭ\xc8\xcf\xd3\xef\xd1ԡ\xa3
      /Log			\xbd\xab IDE \xbb\xbc\xc7¼\xb5\xbdָ\xb6\xa8\xb5\xc4\xceļ\xfe\xd2Խ\xf8\xd0\xd0\xd2\xc9\xc4ѽ\xe2\xb4\xf0\xa1\xa3
      /NoVSIP			\xbd\xfb\xd3\xc3\xd3\xc3\xd3\xda VSIP \xb2\xe2\xcaԵ\xc4 VSIP \xbf\xaa\xb7\xa2\xc8\xcbԱ\xd0\xed\xbf\xc9֤\xc3\xdcԿ\xa1\xa3
      /Out			\xbd\xab\xc9\xfa\xb3\xc9\xc8\xd5־׷\xbcӵ\xbdָ\xb6\xa8\xb5\xc4\xceļ\xfe\xd6С\xa3
      /Project		ָ\xb6\xa8Ҫ\xc9\xfa\xb3ɡ\xa2\xc7\xe5\xc0\xed\xbb\xf2\xb2\xbf\xca\xf0\xb5\xc4\xcf\xeeĿ\xa1\xa3
      			\xb1\xd8\xd0\xeb\xba\xcd /Build\xa1\xa2/Rebuild\xa1\xa2/Clean \xbb\xf2 /Deploy һ\xc6\xf0ʹ\xd3á\xa3
      /ProjectConfig		\xd6\xd8д\xbd\xe2\xbe\xf6\xb7\xbd\xb0\xb8\xc5\xe4\xd6\xc3\xd6\xd0ָ\xb6\xa8\xb5\xc4\xcf\xeeĿ
      			\xc5\xe4\xd6á\xa3\xc0\xfd\xc8\xe7 "Debug"\xa1\xa3\xc8\xe7\xb9\xfb\xbf\xc9\xc4ܴ\xe6\xd4\xda
      			\xb6\xe0\xb8\xf6ƽ̨\xa3\xac\xd4\xf2\xc5\xe4\xd6\xc3\xc3\xfb\xb3Ʊ\xd8\xd0\xeb\xd3\xc3\xd2\xfd\xba\xc5\xc0\xa8\xc6\xf0\xc0\xb4
      			\xb2\xa2\xb0\xfc\xba\xacƽ̨\xc3\xfb\xb3ơ\xa3\xc0\xfd\xc8\xe7 "Debug|Win32"\xa1\xa3
      			\xb1\xd8\xd0\xeb\xba\xcd /Project һ\xc6\xf0ʹ\xd3á\xa3
      /Rebuild		\xcf\xc8\xc7\xe5\xc0\xed\xa3\xacȻ\xba\xf3ʹ\xd3\xc3ָ\xb6\xa8\xc5\xe4\xd6\xc3\xc9\xfa\xb3ɽ\xe2\xbe\xf6\xb7\xbd\xb0\xb8
      			\xbb\xf2\xcf\xeeĿ\xa1\xa3
      /ResetSettings		\xbbָ\xb4 IDE \xb5\xc4Ĭ\xc8\xcf\xc9\xe8\xd6ã\xac\xbb\xb9\xbf\xc9\xd2\xd4ѡ\xd4\xf1\xd6\xd8\xd6\xc3Ϊ
      			ָ\xb6\xa8\xb5\xc4 VSSettings \xceļ\xfe\xa1\xa3
      /ResetSkipPkgs		\xc7\xe5\xb3\xfd\xcb\xf9\xd3\xd0\xcc\xed\xbcӵ\xbd VSPackages \xb5\xc4 SkipLoading \xb1\xea\xbcǡ\xa3
      /Run			\xb1\xe0\xd2벢\xd4\xcb\xd0\xd0ָ\xb6\xa8\xb5Ľ\xe2\xbe\xf6\xb7\xbd\xb0\xb8\xa1\xa3
      /RunExit		\xb1\xe0\xd2벢\xd4\xcb\xd0\xd0ָ\xb6\xa8\xb5Ľ\xe2\xbe\xf6\xb7\xbd\xb0\xb8Ȼ\xba\xf3\xb9ر\xd5 IDE\xa1\xa3
      /SafeMode		\xd2԰\xb2ȫģʽ\xc6\xf4\xb6\xaf IDE\xa3\xac\xbc\xd3\xd4\xd8\xd7\xee\xc9\xd9\xca\xfd\xc1\xbf\xb5Ĵ\xb0\xbfڡ\xa3
      /Upgrade		\xc9\xfd\xbc\xb6\xcf\xeeĿ\xbb\xf2\xbd\xe2\xbe\xf6\xb7\xbd\xb0\xb8\xd2Լ\xb0\xc6\xe4\xd6е\xc4\xcb\xf9\xd3\xd0\xcf\xeeĿ\xa1\xa3
      			\xb2\xa2\xcf\xe0Ӧ\xb5ش\xb4\xbd\xa8\xd5\xe2Щ\xceļ\xfe\xb5ı\xb8\xb7ݡ\xa3 \xd3йر\xb8\xb7\xdd
      			\xb9\xfd\xb3̵\xc4\xcf\xeaϸ\xd0\xc5Ϣ\xa3\xac\xc7\xeb\xb2μ\xfb
      			\xa1\xb0Visual Studio ת\xbb\xbb\xcf򵼡\xb1\xc9ϵİ\xef\xd6\xfa\xa1\xa3
      
      \xb2\xfaƷ\xccض\xa8\xb5Ŀ\xaa\xb9\xd8: 
      
      /debugexe	\xb4\xf2\xbf\xaaҪ\xb5\xf7\xcaԵ\xc4ָ\xb6\xa8\xbf\xc9ִ\xd0\xd0\xceļ\xfe\xa1\xa3\xbd\xab 
      		\xc3\xfc\xc1\xee\xd0е\xc4ʣ\xd3ಿ\xb7\xd6\xd7\xf7Ϊ\xb2\xce\xca\xfd\xb4\xab\xb5ݸ\xf8\xb4˿\xc9ִ\xd0\xd0\xceļ\xfe\xa1\xa3
      /diff		\xb1Ƚ\xcf\xc1\xbd\xb8\xf6\xceļ\xfe\xa1\xa3ʹ\xd3\xc3\xcbĸ\xf6\xb2\xce\xca\xfd\xa3\xba
      		SourceFile\xa1\xa2TargetFile\xa1\xa2SourceDisplayName(optional)\xa1\xa2
      		TargetDisplayName(optional)
      /TfsLink	\xb4\xf2\xbf\xaa\xcdŶ\xd3\xd7\xcaԴ\xb9\xdc\xc0\xed\xc6\xf7\xb2\xa2Ϊ\xccṩ\xb5\xc4\xcf\xeeĿ URI \xc6\xf4\xb6\xaf\xb2鿴\xc6\xf7
      		(\xc8\xe7\xb9\xfbע\xb2\xe1\xc1\xcb\xcf\xeeĿ URI)\xa1\xa3
       /useenv		ʹ\xd3\xc3 PATH\xa1\xa2INCLUDE\xa1\xa2LIBPATH \xba\xcd LIB \xbb\xb7\xbe\xb3\xb1\xe4\xc1\xbf
      		\xb6\xf8\xb2\xbb\xca\xc7ʹ\xd3\xc3 VC++ \xc9\xfa\xb3ɵ\xc4 IDE ·\xbe\xb6\xa1\xa3
      
      Ҫ\xb4\xd3\xc3\xfc\xc1\xee\xd0и\xbd\xbcӵ\xf7\xca\xd4\xc6\xf7\xa3\xac\xc7\xebʹ\xd3\xc3:
      	VsJITDebugger.exe -p <pid>
      
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineFortranCompiler.cmake:190 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:5 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeFortranCompilerId.F.in"
    candidate_directories:
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/share/cmake-4.1/Modules/"
    found: "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/share/cmake-4.1/Modules/CMakeFortranCompilerId.F.in"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX86\\x86"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\VC\\VCPackages"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\TestWindow"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\TeamFoundation\\Team Explorer"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\bin\\Roslyn"
        - "C:\\Program Files (x86)\\Microsoft SDKs\\Windows\\v10.0A\\bin\\NETFX 4.8 Tools\\"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Team Tools\\DiagnosticsHub\\Collector"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\bin\\10.0.26100.0\\\\x86"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\bin\\\\x86"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\\\MSBuild\\Current\\Bin\\amd64"
        - "C:\\Windows\\Microsoft.NET\\Framework\\v4.0.30319"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\Tools\\"
        - "D:\\mpi\\Bin\\"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\ProgramData\\Oracle\\Java\\javapath"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\windows\\system32"
        - "C:\\windows"
        - "C:\\windows\\System32\\Wbem"
        - "C:\\windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\windows\\System32\\OpenSSH\\"
        - "C:\\sqlite"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Program Files (x86)\\pcsuite\\"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "D:\\CAMKE\\bin"
        - "D:\\mingw\\mingw64\\bin"
        - "C:\\Users\\<USER>\\Downloads\\x86_64-15.1.0-release-win32-seh-ucrt-rt_v12-rev0\\mingw64\\bin"
        - "D:\\matlab\\runtime\\win64"
        - "D:\\matlab\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\Downloads\\clion\\CLion 2025.1.2\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "E:\\Git\\cmd"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft V"
        - "C:\\Program Files\\nodejs\\"
        - "D:\\OpenBLAS-0.3.30-x64"
        - "D:\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\Downloads\\clion\\CLion 2025.1.2\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\Downloads\\cmake-4.1.0-rc4-windows-x86_64\\cmake-4.1.0-rc4-windows-x86_64\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Trae\\resources\\app\\bin\\lib"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\bin"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\Ninja"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\VC\\Linux\\bin\\ConnectionManagerExe"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\vcpkg"
      ENV{INCLUDE}:
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\\\include\\10.0.26100.0\\\\um"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\\\include\\10.0.26100.0\\\\shared"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\\\include\\10.0.26100.0\\\\winrt"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\\\include\\10.0.26100.0\\\\cppwinrt"
        - "C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um"
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineFortranCompiler.cmake:190 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:5 (project)"
    message: |
      Compiling the Fortran compiler identification source file "CMakeFortranCompilerId.F" failed.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      1
      
      Microsoft Visual Studio 2022 \xb0汾 17.14.6 (June 2025).
      \xb0\xe6Ȩ\xcb\xf9\xd3\xd0(C) Microsoft Corp.\xa1\xa3\xb1\xa3\xc1\xf4\xcb\xf9\xd3\xd0Ȩ\xc0\xfb\xa1\xa3
      
      \xbd\xe2\xbe\xf6\xb7\xbd\xb0\xb8\xc5\xe4\xd6\xc3\xce\xdeЧ
      
      \xd3÷\xa8: 
      devenv [\xbd\xe2\xbe\xf6\xb7\xbd\xb0\xb8\xceļ\xfe | \xcf\xeeĿ\xceļ\xfe | \xceļ\xfe\xbc\xd0 | \xc8\xce\xd2\xe2\xceļ\xfe.\xc0\xa9չ\xc3\xfb] [\xbf\xaa\xb9\xd8]
      
      devenv \xb5ĵ\xdaһ\xb8\xf6\xb2\xce\xca\xfdͨ\xb3\xa3\xca\xc7һ\xb8\xf6\xbd\xe2\xbe\xf6\xb7\xbd\xb0\xb8\xceļ\xfe\xa1\xa2\xcf\xeeĿ\xceļ\xfe\xbb\xf2\xceļ\xfe\xbcС\xa3
      \xc4㻹\xbf\xc9\xd2\xd4ʹ\xd3\xc3\xd4ڱ༭\xc6\xf7\xd6\xd0\xd7Զ\xaf\xb4\xf2\xbf\xaa\xceļ\xfe\xa3\xac
      Ҳ\xbf\xc9\xd2\xd4ʹ\xd3\xc3\xc8κ\xce\xc6\xe4\xcb\xfb\xceļ\xfe\xd7\xf7Ϊ\xb5\xdaһ\xb8\xf6\xb2\xce\xca\xfd\xa1\xa3\xb5\xb1\xc4\xfa\xca\xe4\xc8\xeb\xcf\xeeĿ\xceļ\xfeʱ\xa3\xacIDE
      \xbb\xe1\xd4\xda\xcf\xeeĿ\xceļ\xfe\xb5ĸ\xb8Ŀ¼\xd6в\xe9\xd5\xd2\xd3\xeb\xb8\xc3\xcf\xeeĿ\xceļ\xfe\xbe\xdf\xd3\xd0\xcf\xe0ͬ
      \xbb\xf9\xc3\xfb\xb3Ƶ\xc4 .sln \xceļ\xfe\xa1\xa3\xc8\xe7\xb9\xfb\xb2\xbb\xb4\xe6\xd4\xda\xd5\xe2\xd1\xf9\xb5\xc4 .sln \xceļ\xfe\xa3\xac
      IDE \xbd\xab\xb2\xe9\xd5\xd2\xd2\xfd\xd3ø\xc3\xcf\xeeĿ\xb5ĵ\xa5\xb8\xf6 .sln \xceļ\xfe\xa1\xa3\xc8\xe7\xb9\xfb\xb2\xbb\xb4\xe6\xd4\xda\xd5\xe2\xd1\xf9\xb5ĵ\xa5\xb8\xf6
      .sln \xceļ\xfe\xa3\xac\xd4\xf2 IDE \xbd\xab\xb4\xb4\xbd\xa8һ\xb8\xf6\xbe\xdf\xd3\xd0Ĭ\xc8\xcf .sln \xceļ\xfe\xc3\xfb\xb5\xc4δ\xb1\xa3\xb4\xe6
      \xb5Ľ\xe2\xbe\xf6\xb7\xbd\xb0\xb8\xa3\xac\xc6\xe4\xbb\xf9\xc3\xfb\xb3\xc6\xd3\xeb\xcf\xeeĿ\xceļ\xfe\xb5Ļ\xf9\xc3\xfb\xb3\xc6\xcf\xe0ͬ\xa1\xa3
      
      \xc3\xfc\xc1\xee\xd0\xd0\xc9\xfa\xb3\xc9: 
      devenv \xbd\xe2\xbe\xf6\xb7\xbd\xb0\xb8\xceļ\xfe.sln /build [ \xbd\xe2\xbe\xf6\xb7\xbd\xb0\xb8\xc5\xe4\xd6\xc3 ] [ /project \xcf\xeeĿ\xc3\xfb\xb3ƻ\xf2\xceļ\xfe [ /projectconfig \xc3\xfb\xb3\xc6 ] ]
      \xbf\xc9\xd3õ\xc4\xc3\xfc\xc1\xee\xd0п\xaa\xb9\xd8: 
      
      /Build			ʹ\xd3\xc3ָ\xb6\xa8\xb5Ľ\xe2\xbe\xf6\xb7\xbd\xb0\xb8\xc5\xe4\xd6\xc3\xc9\xfa\xb3ɽ\xe2\xbe\xf6\xb7\xbd\xb0\xb8\xbb\xf2
      			\xcf\xeeĿ\xa1\xa3\xc0\xfd\xc8\xe7 "Debug"\xa1\xa3\xc8\xe7\xb9\xfb\xbf\xc9\xc4ܴ\xe6\xd4ڶ\xe0\xb8\xf6
      			ƽ̨\xa3\xac\xd4\xf2\xc5\xe4\xd6\xc3\xc3\xfb\xb3Ʊ\xd8\xd0\xeb\xd3\xc3\xd2\xfd\xba\xc5\xc0\xa8\xc6\xf0\xc0\xb4
      			\xb2\xa2\xb0\xfc\xba\xacƽ̨\xc3\xfb\xb3ơ\xa3\xc0\xfd\xc8\xe7 "Debug|Win32"\xa1\xa3
      /Clean			ɾ\xb3\xfd\xc9\xfa\xb3ɽ\xe1\xb9\xfb\xa1\xa3
      /Command		\xc6\xf4\xb6\xaf IDE \xb2\xa2ִ\xd0и\xc3\xc3\xfc\xc1
      /Deploy			\xc9\xfa\xb3ɲ\xa2\xb2\xbf\xca\xf0ָ\xb6\xa8\xb5\xc4\xc9\xfa\xb3\xc9\xc5\xe4\xd6á\xa3
      /DoNotLoadProjects	\xb4\xf2\xbf\xaaָ\xb6\xa8\xb5Ľ\xe2\xbe\xf6\xb7\xbd\xb0\xb8\xc7Ҳ\xbb\xbc\xd3\xd4\xd8\xc8κ\xce\xcf\xeeĿ\xa1\xa3
      /Edit			\xd4ڴ\xcbӦ\xd3ó\xcc\xd0\xf2\xb5\xc4\xd4\xcb\xd0\xd0ʵ\xc0\xfd\xd6д\xf2\xbf\xaa
      			ָ\xb6\xa8\xb5\xc4\xceļ\xfe\xa1\xa3\xc8\xe7\xb9\xfbû\xd3\xd0\xd5\xfd\xd4\xda\xd4\xcb\xd0е\xc4ʵ\xc0\xfd\xa3\xac
      			\xd4\xf2\xc6\xf4\xb6\xafһ\xb8\xf6\xbe\xdf\xd3м򻯴\xb0\xbfڲ\xbc\xbeֵ\xc4\xd0\xc2ʵ\xc0\xfd\xa1\xa3
      /LCID			\xc9\xe8\xd6\xc3 IDE \xd6\xd0\xd3\xc3\xd3\xda\xd3û\xa7\xbd\xe7\xc3\xe6\xb5\xc4Ĭ\xc8\xcf\xd3\xef\xd1ԡ\xa3
      /Log			\xbd\xab IDE \xbb\xbc\xc7¼\xb5\xbdָ\xb6\xa8\xb5\xc4\xceļ\xfe\xd2Խ\xf8\xd0\xd0\xd2\xc9\xc4ѽ\xe2\xb4\xf0\xa1\xa3
      /NoVSIP			\xbd\xfb\xd3\xc3\xd3\xc3\xd3\xda VSIP \xb2\xe2\xcaԵ\xc4 VSIP \xbf\xaa\xb7\xa2\xc8\xcbԱ\xd0\xed\xbf\xc9֤\xc3\xdcԿ\xa1\xa3
      /Out			\xbd\xab\xc9\xfa\xb3\xc9\xc8\xd5־׷\xbcӵ\xbdָ\xb6\xa8\xb5\xc4\xceļ\xfe\xd6С\xa3
      /Project		ָ\xb6\xa8Ҫ\xc9\xfa\xb3ɡ\xa2\xc7\xe5\xc0\xed\xbb\xf2\xb2\xbf\xca\xf0\xb5\xc4\xcf\xeeĿ\xa1\xa3
      			\xb1\xd8\xd0\xeb\xba\xcd /Build\xa1\xa2/Rebuild\xa1\xa2/Clean \xbb\xf2 /Deploy һ\xc6\xf0ʹ\xd3á\xa3
      /ProjectConfig		\xd6\xd8д\xbd\xe2\xbe\xf6\xb7\xbd\xb0\xb8\xc5\xe4\xd6\xc3\xd6\xd0ָ\xb6\xa8\xb5\xc4\xcf\xeeĿ
      			\xc5\xe4\xd6á\xa3\xc0\xfd\xc8\xe7 "Debug"\xa1\xa3\xc8\xe7\xb9\xfb\xbf\xc9\xc4ܴ\xe6\xd4\xda
      			\xb6\xe0\xb8\xf6ƽ̨\xa3\xac\xd4\xf2\xc5\xe4\xd6\xc3\xc3\xfb\xb3Ʊ\xd8\xd0\xeb\xd3\xc3\xd2\xfd\xba\xc5\xc0\xa8\xc6\xf0\xc0\xb4
      			\xb2\xa2\xb0\xfc\xba\xacƽ̨\xc3\xfb\xb3ơ\xa3\xc0\xfd\xc8\xe7 "Debug|Win32"\xa1\xa3
      			\xb1\xd8\xd0\xeb\xba\xcd /Project һ\xc6\xf0ʹ\xd3á\xa3
      /Rebuild		\xcf\xc8\xc7\xe5\xc0\xed\xa3\xacȻ\xba\xf3ʹ\xd3\xc3ָ\xb6\xa8\xc5\xe4\xd6\xc3\xc9\xfa\xb3ɽ\xe2\xbe\xf6\xb7\xbd\xb0\xb8
      			\xbb\xf2\xcf\xeeĿ\xa1\xa3
      /ResetSettings		\xbbָ\xb4 IDE \xb5\xc4Ĭ\xc8\xcf\xc9\xe8\xd6ã\xac\xbb\xb9\xbf\xc9\xd2\xd4ѡ\xd4\xf1\xd6\xd8\xd6\xc3Ϊ
      			ָ\xb6\xa8\xb5\xc4 VSSettings \xceļ\xfe\xa1\xa3
      /ResetSkipPkgs		\xc7\xe5\xb3\xfd\xcb\xf9\xd3\xd0\xcc\xed\xbcӵ\xbd VSPackages \xb5\xc4 SkipLoading \xb1\xea\xbcǡ\xa3
      /Run			\xb1\xe0\xd2벢\xd4\xcb\xd0\xd0ָ\xb6\xa8\xb5Ľ\xe2\xbe\xf6\xb7\xbd\xb0\xb8\xa1\xa3
      /RunExit		\xb1\xe0\xd2벢\xd4\xcb\xd0\xd0ָ\xb6\xa8\xb5Ľ\xe2\xbe\xf6\xb7\xbd\xb0\xb8Ȼ\xba\xf3\xb9ر\xd5 IDE\xa1\xa3
      /SafeMode		\xd2԰\xb2ȫģʽ\xc6\xf4\xb6\xaf IDE\xa3\xac\xbc\xd3\xd4\xd8\xd7\xee\xc9\xd9\xca\xfd\xc1\xbf\xb5Ĵ\xb0\xbfڡ\xa3
      /Upgrade		\xc9\xfd\xbc\xb6\xcf\xeeĿ\xbb\xf2\xbd\xe2\xbe\xf6\xb7\xbd\xb0\xb8\xd2Լ\xb0\xc6\xe4\xd6е\xc4\xcb\xf9\xd3\xd0\xcf\xeeĿ\xa1\xa3
      			\xb2\xa2\xcf\xe0Ӧ\xb5ش\xb4\xbd\xa8\xd5\xe2Щ\xceļ\xfe\xb5ı\xb8\xb7ݡ\xa3 \xd3йر\xb8\xb7\xdd
      			\xb9\xfd\xb3̵\xc4\xcf\xeaϸ\xd0\xc5Ϣ\xa3\xac\xc7\xeb\xb2μ\xfb
      			\xa1\xb0Visual Studio ת\xbb\xbb\xcf򵼡\xb1\xc9ϵİ\xef\xd6\xfa\xa1\xa3
      
      \xb2\xfaƷ\xccض\xa8\xb5Ŀ\xaa\xb9\xd8: 
      
      /debugexe	\xb4\xf2\xbf\xaaҪ\xb5\xf7\xcaԵ\xc4ָ\xb6\xa8\xbf\xc9ִ\xd0\xd0\xceļ\xfe\xa1\xa3\xbd\xab 
      		\xc3\xfc\xc1\xee\xd0е\xc4ʣ\xd3ಿ\xb7\xd6\xd7\xf7Ϊ\xb2\xce\xca\xfd\xb4\xab\xb5ݸ\xf8\xb4˿\xc9ִ\xd0\xd0\xceļ\xfe\xa1\xa3
      /diff		\xb1Ƚ\xcf\xc1\xbd\xb8\xf6\xceļ\xfe\xa1\xa3ʹ\xd3\xc3\xcbĸ\xf6\xb2\xce\xca\xfd\xa3\xba
      		SourceFile\xa1\xa2TargetFile\xa1\xa2SourceDisplayName(optional)\xa1\xa2
      		TargetDisplayName(optional)
      /TfsLink	\xb4\xf2\xbf\xaa\xcdŶ\xd3\xd7\xcaԴ\xb9\xdc\xc0\xed\xc6\xf7\xb2\xa2Ϊ\xccṩ\xb5\xc4\xcf\xeeĿ URI \xc6\xf4\xb6\xaf\xb2鿴\xc6\xf7
      		(\xc8\xe7\xb9\xfbע\xb2\xe1\xc1\xcb\xcf\xeeĿ URI)\xa1\xa3
       /useenv		ʹ\xd3\xc3 PATH\xa1\xa2INCLUDE\xa1\xa2LIBPATH \xba\xcd LIB \xbb\xb7\xbe\xb3\xb1\xe4\xc1\xbf
      		\xb6\xf8\xb2\xbb\xca\xc7ʹ\xd3\xc3 VC++ \xc9\xfa\xb3ɵ\xc4 IDE ·\xbe\xb6\xa1\xa3
      
      Ҫ\xb4\xd3\xc3\xfc\xc1\xee\xd0и\xbd\xbcӵ\xf7\xca\xd4\xc6\xf7\xa3\xac\xc7\xebʹ\xd3\xc3:
      	VsJITDebugger.exe -p <pid>
      
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:37 (find_program)"
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:65 (__resolve_tool_path)"
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:104 (__resolve_linker_path)"
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineFortranCompiler.cmake:313 (include)"
      - "CMakeLists.txt:5 (project)"
    mode: "program"
    variable: "_CMAKE_TOOL_WITH_PATH"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "lld-link"
    candidate_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx86/x86/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/vcpackages/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TestWindow/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TeamFoundation/Team Explorer/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/Roslyn/"
      - "C:/Program Files (x86)/Microsoft SDKs/Windows/v10.0A/bin/NETFX 4.8 Tools/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Team Tools/DiagnosticsHub/Collector/"
      - "C:/Program Files (x86)/Windows Kits/10/bin/10.0.26100.0/x86/"
      - "C:/Program Files (x86)/Windows Kits/10/bin/x86/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/"
      - "C:/Windows/Microsoft.NET/Framework/v4.0.30319/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/Tools/"
      - "D:/mpi/Bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/ProgramData/Oracle/Java/javapath/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/sqlite/"
      - "C:/Program Files/dotnet/"
      - "C:/Program Files (x86)/pcsuite/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "D:/CAMKE/bin/"
      - "D:/mingw/mingw64/bin/"
      - "C:/Users/<USER>/Downloads/x86_64-15.1.0-release-win32-seh-ucrt-rt_v12-rev0/mingw64/bin/"
      - "D:/matlab/runtime/win64/"
      - "D:/matlab/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/.dotnet/tools/"
      - "C:/Users/<USER>/Downloads/clion/CLion 2025.1.2/bin/"
      - "E:/Git/cmd/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft V/"
      - "C:/Program Files/nodejs/"
      - "D:/OpenBLAS-0.3.30-x64/"
      - "D:/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Trae/resources/app/bin/lib/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/Linux/bin/ConnectionManagerExe/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/vcpkg/"
    searched_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx86/x86/lld-link.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx86/x86/lld-link.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx86/x86/lld-link"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/vcpackages/lld-link.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/vcpackages/lld-link.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/vcpackages/lld-link"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TestWindow/lld-link.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TestWindow/lld-link.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TestWindow/lld-link"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TeamFoundation/Team Explorer/lld-link.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TeamFoundation/Team Explorer/lld-link.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TeamFoundation/Team Explorer/lld-link"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/Roslyn/lld-link.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/Roslyn/lld-link.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/Roslyn/lld-link"
      - "C:/Program Files (x86)/Microsoft SDKs/Windows/v10.0A/bin/NETFX 4.8 Tools/lld-link.com"
      - "C:/Program Files (x86)/Microsoft SDKs/Windows/v10.0A/bin/NETFX 4.8 Tools/lld-link.exe"
      - "C:/Program Files (x86)/Microsoft SDKs/Windows/v10.0A/bin/NETFX 4.8 Tools/lld-link"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Team Tools/DiagnosticsHub/Collector/lld-link.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Team Tools/DiagnosticsHub/Collector/lld-link.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Team Tools/DiagnosticsHub/Collector/lld-link"
      - "C:/Program Files (x86)/Windows Kits/10/bin/10.0.26100.0/x86/lld-link.com"
      - "C:/Program Files (x86)/Windows Kits/10/bin/10.0.26100.0/x86/lld-link.exe"
      - "C:/Program Files (x86)/Windows Kits/10/bin/10.0.26100.0/x86/lld-link"
      - "C:/Program Files (x86)/Windows Kits/10/bin/x86/lld-link.com"
      - "C:/Program Files (x86)/Windows Kits/10/bin/x86/lld-link.exe"
      - "C:/Program Files (x86)/Windows Kits/10/bin/x86/lld-link"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/lld-link.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/lld-link.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/lld-link"
      - "C:/Windows/Microsoft.NET/Framework/v4.0.30319/lld-link.com"
      - "C:/Windows/Microsoft.NET/Framework/v4.0.30319/lld-link.exe"
      - "C:/Windows/Microsoft.NET/Framework/v4.0.30319/lld-link"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/lld-link.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/lld-link.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/lld-link"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/Tools/lld-link.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/Tools/lld-link.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/Tools/lld-link"
      - "D:/mpi/Bin/lld-link.com"
      - "D:/mpi/Bin/lld-link.exe"
      - "D:/mpi/Bin/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/lld-link"
      - "C:/ProgramData/Oracle/Java/javapath/lld-link.com"
      - "C:/ProgramData/Oracle/Java/javapath/lld-link.exe"
      - "C:/ProgramData/Oracle/Java/javapath/lld-link"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/lld-link.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/lld-link.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/lld-link"
      - "C:/Windows/System32/lld-link.com"
      - "C:/Windows/System32/lld-link.exe"
      - "C:/Windows/System32/lld-link"
      - "C:/Windows/lld-link.com"
      - "C:/Windows/lld-link.exe"
      - "C:/Windows/lld-link"
      - "C:/Windows/System32/wbem/lld-link.com"
      - "C:/Windows/System32/wbem/lld-link.exe"
      - "C:/Windows/System32/wbem/lld-link"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link"
      - "C:/Windows/System32/OpenSSH/lld-link.com"
      - "C:/Windows/System32/OpenSSH/lld-link.exe"
      - "C:/Windows/System32/OpenSSH/lld-link"
      - "C:/sqlite/lld-link.com"
      - "C:/sqlite/lld-link.exe"
      - "C:/sqlite/lld-link"
      - "C:/Program Files/dotnet/lld-link.com"
      - "C:/Program Files/dotnet/lld-link.exe"
      - "C:/Program Files/dotnet/lld-link"
      - "C:/Program Files (x86)/pcsuite/lld-link.com"
      - "C:/Program Files (x86)/pcsuite/lld-link.exe"
      - "C:/Program Files (x86)/pcsuite/lld-link"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/lld-link.com"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/lld-link.exe"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/lld-link"
      - "D:/CAMKE/bin/lld-link.com"
      - "D:/CAMKE/bin/lld-link.exe"
      - "D:/CAMKE/bin/lld-link"
      - "D:/mingw/mingw64/bin/lld-link.com"
      - "D:/mingw/mingw64/bin/lld-link.exe"
      - "D:/mingw/mingw64/bin/lld-link"
      - "C:/Users/<USER>/Downloads/x86_64-15.1.0-release-win32-seh-ucrt-rt_v12-rev0/mingw64/bin/lld-link.com"
      - "C:/Users/<USER>/Downloads/x86_64-15.1.0-release-win32-seh-ucrt-rt_v12-rev0/mingw64/bin/lld-link.exe"
      - "C:/Users/<USER>/Downloads/x86_64-15.1.0-release-win32-seh-ucrt-rt_v12-rev0/mingw64/bin/lld-link"
      - "D:/matlab/runtime/win64/lld-link.com"
      - "D:/matlab/runtime/win64/lld-link.exe"
      - "D:/matlab/runtime/win64/lld-link"
      - "D:/matlab/bin/lld-link.com"
      - "D:/matlab/bin/lld-link.exe"
      - "D:/matlab/bin/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Scripts/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Scripts/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Scripts/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/lld-link"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link"
      - "C:/Users/<USER>/.dotnet/tools/lld-link.com"
      - "C:/Users/<USER>/.dotnet/tools/lld-link.exe"
      - "C:/Users/<USER>/.dotnet/tools/lld-link"
      - "C:/Users/<USER>/Downloads/clion/CLion 2025.1.2/bin/lld-link.com"
      - "C:/Users/<USER>/Downloads/clion/CLion 2025.1.2/bin/lld-link.exe"
      - "C:/Users/<USER>/Downloads/clion/CLion 2025.1.2/bin/lld-link"
      - "E:/Git/cmd/lld-link.com"
      - "E:/Git/cmd/lld-link.exe"
      - "E:/Git/cmd/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft V/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft V/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft V/lld-link"
      - "C:/Program Files/nodejs/lld-link.com"
      - "C:/Program Files/nodejs/lld-link.exe"
      - "C:/Program Files/nodejs/lld-link"
      - "D:/OpenBLAS-0.3.30-x64/lld-link.com"
      - "D:/OpenBLAS-0.3.30-x64/lld-link.exe"
      - "D:/OpenBLAS-0.3.30-x64/lld-link"
      - "D:/Microsoft VS Code/bin/lld-link.com"
      - "D:/Microsoft VS Code/bin/lld-link.exe"
      - "D:/Microsoft VS Code/bin/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/lld-link"
      - "C:/Users/<USER>/AppData/Roaming/npm/lld-link.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/lld-link.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/lld-link"
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/bin/lld-link.com"
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/bin/lld-link.exe"
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/bin/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Trae/resources/app/bin/lib/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Trae/resources/app/bin/lib/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Trae/resources/app/bin/lib/lld-link"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/lld-link.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/lld-link.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/lld-link"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/lld-link.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/lld-link.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/lld-link"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/Linux/bin/ConnectionManagerExe/lld-link.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/Linux/bin/ConnectionManagerExe/lld-link.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/Linux/bin/ConnectionManagerExe/lld-link"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/vcpkg/lld-link.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/vcpkg/lld-link.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/vcpkg/lld-link"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX86\\x86"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\VC\\VCPackages"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\TestWindow"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\TeamFoundation\\Team Explorer"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\bin\\Roslyn"
        - "C:\\Program Files (x86)\\Microsoft SDKs\\Windows\\v10.0A\\bin\\NETFX 4.8 Tools\\"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Team Tools\\DiagnosticsHub\\Collector"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\bin\\10.0.26100.0\\\\x86"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\bin\\\\x86"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\\\MSBuild\\Current\\Bin\\amd64"
        - "C:\\Windows\\Microsoft.NET\\Framework\\v4.0.30319"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\Tools\\"
        - "D:\\mpi\\Bin\\"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\ProgramData\\Oracle\\Java\\javapath"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\windows\\system32"
        - "C:\\windows"
        - "C:\\windows\\System32\\Wbem"
        - "C:\\windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\windows\\System32\\OpenSSH\\"
        - "C:\\sqlite"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Program Files (x86)\\pcsuite\\"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "D:\\CAMKE\\bin"
        - "D:\\mingw\\mingw64\\bin"
        - "C:\\Users\\<USER>\\Downloads\\x86_64-15.1.0-release-win32-seh-ucrt-rt_v12-rev0\\mingw64\\bin"
        - "D:\\matlab\\runtime\\win64"
        - "D:\\matlab\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\Downloads\\clion\\CLion 2025.1.2\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "E:\\Git\\cmd"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft V"
        - "C:\\Program Files\\nodejs\\"
        - "D:\\OpenBLAS-0.3.30-x64"
        - "D:\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\Downloads\\clion\\CLion 2025.1.2\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\Downloads\\cmake-4.1.0-rc4-windows-x86_64\\cmake-4.1.0-rc4-windows-x86_64\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Trae\\resources\\app\\bin\\lib"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\bin"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\Ninja"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\VC\\Linux\\bin\\ConnectionManagerExe"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\vcpkg"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineRCCompiler.cmake:40 (find_program)"
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/share/cmake-4.1/Modules/Platform/Windows-MSVC.cmake:573 (enable_language)"
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/share/cmake-4.1/Modules/Platform/Windows-MSVC.cmake:546 (__windows_compiler_msvc_enable_rc)"
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/share/cmake-4.1/Modules/Platform/Windows-MSVC-C.cmake:5 (__windows_compiler_msvc)"
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/share/cmake-4.1/Modules/CMakeCInformation.cmake:48 (include)"
      - "CMakeLists.txt:5 (project)"
    mode: "program"
    variable: "CMAKE_RC_COMPILER"
    description: "RC compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "rc"
    candidate_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx86/x86/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/vcpackages/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TestWindow/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TeamFoundation/Team Explorer/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/Roslyn/"
      - "C:/Program Files (x86)/Microsoft SDKs/Windows/v10.0A/bin/NETFX 4.8 Tools/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Team Tools/DiagnosticsHub/Collector/"
      - "C:/Program Files (x86)/Windows Kits/10/bin/10.0.26100.0/x86/"
      - "C:/Program Files (x86)/Windows Kits/10/bin/x86/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/"
      - "C:/Windows/Microsoft.NET/Framework/v4.0.30319/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/Tools/"
      - "D:/mpi/Bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/ProgramData/Oracle/Java/javapath/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/sqlite/"
      - "C:/Program Files/dotnet/"
      - "C:/Program Files (x86)/pcsuite/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "D:/CAMKE/bin/"
      - "D:/mingw/mingw64/bin/"
      - "C:/Users/<USER>/Downloads/x86_64-15.1.0-release-win32-seh-ucrt-rt_v12-rev0/mingw64/bin/"
      - "D:/matlab/runtime/win64/"
      - "D:/matlab/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/.dotnet/tools/"
      - "C:/Users/<USER>/Downloads/clion/CLion 2025.1.2/bin/"
      - "E:/Git/cmd/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft V/"
      - "C:/Program Files/nodejs/"
      - "D:/OpenBLAS-0.3.30-x64/"
      - "D:/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Trae/resources/app/bin/lib/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/Linux/bin/ConnectionManagerExe/"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/vcpkg/"
      - "C:/Program Files/bin/"
      - "C:/Program Files/sbin/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/bin/"
      - "C:/Program Files (x86)/sbin/"
      - "C:/Program Files (x86)/"
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/bin/"
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/sbin/"
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/"
      - "C:/Program Files (x86)/FortranCInterface/bin/"
      - "C:/Program Files (x86)/FortranCInterface/sbin/"
      - "C:/Program Files (x86)/FortranCInterface/"
    searched_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx86/x86/rc.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx86/x86/rc.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx86/x86/rc"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/vcpackages/rc.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/vcpackages/rc.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/VC/vcpackages/rc"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TestWindow/rc.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TestWindow/rc.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TestWindow/rc"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TeamFoundation/Team Explorer/rc.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TeamFoundation/Team Explorer/rc.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/TeamFoundation/Team Explorer/rc"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/Roslyn/rc.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/Roslyn/rc.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/Roslyn/rc"
      - "C:/Program Files (x86)/Microsoft SDKs/Windows/v10.0A/bin/NETFX 4.8 Tools/rc.com"
      - "C:/Program Files (x86)/Microsoft SDKs/Windows/v10.0A/bin/NETFX 4.8 Tools/rc.exe"
      - "C:/Program Files (x86)/Microsoft SDKs/Windows/v10.0A/bin/NETFX 4.8 Tools/rc"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Team Tools/DiagnosticsHub/Collector/rc.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Team Tools/DiagnosticsHub/Collector/rc.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Team Tools/DiagnosticsHub/Collector/rc"
      - "C:/Program Files (x86)/Windows Kits/10/bin/10.0.26100.0/x86/rc.com"
    found: "C:/Program Files (x86)/Windows Kits/10/bin/10.0.26100.0/x86/rc.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX86\\x86"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\VC\\VCPackages"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\TestWindow"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\TeamFoundation\\Team Explorer"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\bin\\Roslyn"
        - "C:\\Program Files (x86)\\Microsoft SDKs\\Windows\\v10.0A\\bin\\NETFX 4.8 Tools\\"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Team Tools\\DiagnosticsHub\\Collector"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\bin\\10.0.26100.0\\\\x86"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\bin\\\\x86"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\\\MSBuild\\Current\\Bin\\amd64"
        - "C:\\Windows\\Microsoft.NET\\Framework\\v4.0.30319"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\Tools\\"
        - "D:\\mpi\\Bin\\"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\ProgramData\\Oracle\\Java\\javapath"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\windows\\system32"
        - "C:\\windows"
        - "C:\\windows\\System32\\Wbem"
        - "C:\\windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\windows\\System32\\OpenSSH\\"
        - "C:\\sqlite"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Program Files (x86)\\pcsuite\\"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "D:\\CAMKE\\bin"
        - "D:\\mingw\\mingw64\\bin"
        - "C:\\Users\\<USER>\\Downloads\\x86_64-15.1.0-release-win32-seh-ucrt-rt_v12-rev0\\mingw64\\bin"
        - "D:\\matlab\\runtime\\win64"
        - "D:\\matlab\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\Downloads\\clion\\CLion 2025.1.2\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "E:\\Git\\cmd"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft V"
        - "C:\\Program Files\\nodejs\\"
        - "D:\\OpenBLAS-0.3.30-x64"
        - "D:\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\Downloads\\clion\\CLion 2025.1.2\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\Downloads\\cmake-4.1.0-rc4-windows-x86_64\\cmake-4.1.0-rc4-windows-x86_64\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Trae\\resources\\app\\bin\\lib"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\bin"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\Ninja"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\VC\\Linux\\bin\\ConnectionManagerExe"
        - "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\vcpkg"
      CMAKE_INSTALL_PREFIX: "C:/Program Files (x86)/FortranCInterface"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64"
        - "C:/Program Files (x86)/FortranCInterface"
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/share/cmake-4.1/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:5 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "D:/project/build/CMakeFiles/CMakeScratch/TryCompile-a9xlsq"
      binary: "D:/project/build/CMakeFiles/CMakeScratch/TryCompile-a9xlsq"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Ob0 /Od"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/project/build/CMakeFiles/CMakeScratch/TryCompile-a9xlsq'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_6e8fc.vcxproj /p:Configuration=Debug /p:Platform=win32 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.10+8b8e13593
        鐢熸垚鍚姩鏃堕棿涓?2025/8/2 16:57:42銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淒:\\project\\build\\CMakeFiles\\CMakeScratch\\TryCompile-a9xlsq\\cmTC_6e8fc.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_6e8fc.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淒:\\project\\build\\CMakeFiles\\CMakeScratch\\TryCompile-a9xlsq\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_6e8fc.dir\\Debug\\cmTC_6e8fc.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_6e8fc.dir\\Debug\\cmTC_6e8fc.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_6e8fc.dir\\Debug\\cmTC_6e8fc.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        VcpkgTripletSelection:
          Using triplet "x86-windows" from "C:\\Users\\<USER>\\vcpkg\\installed\\x86-windows\\"
          Using normalized configuration "Debug"
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX86\\x86\\CL.exe /c /I"C:\\Users\\<USER>\\vcpkg\\installed\\x86-windows\\include" /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /Oy- /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Fo"cmTC_6e8fc.dir\\Debug\\\\" /Fd"cmTC_6e8fc.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /analyze- /errorReport:queue "C:\\Users\\<USER>\\Downloads\\cmake-4.1.0-rc4-windows-x86_64\\cmake-4.1.0-rc4-windows-x86_64\\share\\cmake-4.1\\Modules\\CMakeCCompilerABI.c"
          鐢ㄤ簬 x86 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35211 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /I"C:\\Users\\<USER>\\vcpkg\\installed\\x86-windows\\include" /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /Oy- /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Fo"cmTC_6e8fc.dir\\Debug\\\\" /Fd"cmTC_6e8fc.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /analyze- /errorReport:queue "C:\\Users\\<USER>\\Downloads\\cmake-4.1.0-rc4-windows-x86_64\\cmake-4.1.0-rc4-windows-x86_64\\share\\cmake-4.1\\Modules\\CMakeCCompilerABI.c"
          CMakeCCompilerABI.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX86\\x86\\link.exe /OUT:"D:\\project\\build\\CMakeFiles\\CMakeScratch\\TryCompile-a9xlsq\\Debug\\cmTC_6e8fc.exe" /INCREMENTAL /ILK:"cmTC_6e8fc.dir\\Debug\\cmTC_6e8fc.ilk" /NOLOGO /LIBPATH:"C:\\Users\\<USER>\\vcpkg\\installed\\x86-windows\\debug\\lib" /LIBPATH:"C:\\Users\\<USER>\\vcpkg\\installed\\x86-windows\\debug\\lib\\manual-link" kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib "C:\\Users\\<USER>\\vcpkg\\installed\\x86-windows\\debug\\lib\\*.lib" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/project/build/CMakeFiles/CMakeScratch/TryCompile-a9xlsq/Debug/cmTC_6e8fc.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /IMPLIB:"D:/project/build/CMakeFiles/CMakeScratch/TryCompile-a9xlsq/Debug/cmTC_6e8fc.lib" /MACHINE:X86 cmTC_6e8fc.dir\\Debug\\CMakeCCompilerABI.obj
          cmTC_6e8fc.vcxproj -> D:\\project\\build\\CMakeFiles\\CMakeScratch\\TryCompile-a9xlsq\\Debug\\cmTC_6e8fc.exe
        AppLocalFromInstalled:
          pwsh.exe -ExecutionPolicy Bypass -noprofile -File "C:\\Users\\<USER>\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "D:\\project\\build\\CMakeFiles\\CMakeScratch\\TryCompile-a9xlsq\\Debug\\cmTC_6e8fc.exe" "C:\\Users\\<USER>\\vcpkg\\installed\\x86-windows\\debug\\bin" "cmTC_6e8fc.dir\\Debug\\cmTC_6e8fc.tlog\\cmTC_6e8fc.write.1u.tlog" "cmTC_6e8fc.dir\\Debug\\vcpkg.applocal.log"
          'pwsh.exe' 涓嶆槸鍐呴儴鎴栧閮ㄥ懡浠わ紝涔熶笉鏄彲杩愯鐨勭▼搴?
          鎴栨壒澶勭悊鏂囦欢銆?
          鍛戒护鈥減wsh.exe -ExecutionPolicy Bypass -noprofile -File "C:\\Users\\<USER>\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "D:\\project\\build\\CMakeFiles\\CMakeScratch\\TryCompile-a9xlsq\\Debug\\cmTC_6e8fc.exe" "C:\\Users\\<USER>\\vcpkg\\installed\\x86-windows\\debug\\bin" "cmTC_6e8fc.dir\\Debug\\cmTC_6e8fc.tlog\\cmTC_6e8fc.write.1u.tlog" "cmTC_6e8fc.dir\\Debug\\vcpkg.applocal.log"鈥濆凡閫€鍑猴紝浠ｇ爜涓?9009銆?
          "C:\\windows\\System32\\WindowsPowerShell\\v1.0\\powershell.exe" -ExecutionPolicy Bypass -noprofile -File "C:\\Users\\<USER>\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "D:\\project\\build\\CMakeFiles\\CMakeScratch\\TryCompile-a9xlsq\\Debug\\cmTC_6e8fc.exe" "C:\\Users\\<USER>\\vcpkg\\installed\\x86-windows\\debug\\bin" "cmTC_6e8fc.dir\\Debug\\cmTC_6e8fc.tlog\\cmTC_6e8fc.write.1u.tlog" "cmTC_6e8fc.dir\\Debug\\vcpkg.applocal.log"
        FinalizeBuildStatus:
          姝ｅ湪鍒犻櫎鏂囦欢鈥渃mTC_6e8fc.dir\\Debug\\cmTC_6e8fc.tlog\\unsuccessfulbuild鈥濄€?
          姝ｅ湪瀵光€渃mTC_6e8fc.dir\\Debug\\cmTC_6e8fc.tlog\\cmTC_6e8fc.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
        宸插畬鎴愮敓鎴愰」鐩€淒:\\project\\build\\CMakeFiles\\CMakeScratch\\TryCompile-a9xlsq\\cmTC_6e8fc.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
        
        宸叉垚鍔熺敓鎴愩€?
            0 涓鍛?
            0 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:02.92
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:253 (message)"
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/share/cmake-4.1/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:5 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'C': C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/HostX86/x86/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/share/cmake-4.1/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:299 (cmake_determine_linker_id)"
      - "C:/Users/<USER>/Downloads/cmake-4.1.0-rc4-windows-x86_64/cmake-4.1.0-rc4-windows-x86_64/share/cmake-4.1/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:5 (project)"
    message: |
      Running the C compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/HostX86/x86/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.44.35211.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
...
