import pandas as pd
import matplotlib.pyplot as plt 
import sys
import numpy as np

dt = 0.001
elem_x_dir = 12
elem_z_dir = 12
total_elem = elem_x_dir * elem_z_dir

gz_max = 0
gz_min = 0
for i in range(0,10000,500):
    for elm in range(0,total_elem):
        file_step = str("elem_"+str(elm)+"_"+str(i)+".out")
        step_in = pd.read_csv(file_step, header=None)
        step_in = np.array(step_in)
        zmax = step_in.max()
        zmin = step_in.min()
        if zmax > gz_max:
            gz_max = zmax
        if zmin < gz_min:
            gz_min = zmin

print(f"gz_max:{gz_max}, gz_min:{gz_min}")





for i in range(0,10000,500):
    fig, ax = plt.subplots(elem_x_dir,elem_z_dir)
    print(f"step:{i}")
    for elm in range(0,total_elem):
        file_step = str("elem_"+str(elm)+"_"+str(i)+".out")
        step_in = pd.read_csv(file_step, header=None)
        x2d = pd.read_csv("grid_x_"+str(elm), header=None).transpose()
        z2d = pd.read_csv("grid_z_"+str(elm), header=None).transpose()
        x,z = np.meshgrid(x2d,z2d)

        step_in = np.array(step_in)
        plt_row = int(elm%elem_x_dir)
        plt_col = int(elm/elem_z_dir)
        # print(f"elm:{elm}\nrow:{plt_row} \ncol:{plt_col}")
        cmap = plt.colormaps["jet"]

        ax[(elem_x_dir-1)-plt_row,plt_col].pcolormesh(x,z,step_in, cmap=cmap, vmin=gz_min, vmax=gz_max)
        ax[(elem_x_dir-1)-plt_row,plt_col].axis('off')
        ax[(elem_x_dir-1)-plt_row,plt_col].grid('off')
        # ax[4-plt_row,plt_col].text(0.5, 0.5, str(elm), fontsize=18, ha='center')

    plt.pause(1)